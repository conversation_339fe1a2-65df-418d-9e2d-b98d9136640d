# ===============================================================================
# ProGuard Rules for Appio Android App
# ===============================================================================
# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# ===============================================================================
# DEBUGGING & CRASH REPORTING
# ===============================================================================
# Preserve line number information for debugging stack traces
-keepattributes SourceFile,LineNumberTable

# Hide the original source file name for security
-renamesourcefileattribute SourceFile

# Keep exception stack traces readable
-keepattributes Exceptions

# Sentry monitoring rules
-keep class io.sentry.** { *; }
-keep class io.sentry.android.** { *; }
-dontwarn io.sentry.**

# ===============================================================================
# APPLICATION CLASSES
# ===============================================================================
# Keep the main Application class
-keep class so.appio.app.MyApplication { *; }

# Keep all Activities, Services, and BroadcastReceivers
-keep class * extends android.app.Activity
-keep class * extends android.app.Service
-keep class * extends android.content.BroadcastReceiver

# Keep Glance Widget classes
-keep class so.appio.app.widgets.AppioWidget { *; }
-keep class so.appio.app.widgets.AppioWidgetReceiver { *; }
-keep class so.appio.app.widgets.AppioWidgetConfigActivity { *; }
-keep class so.appio.app.widgets.AppioWidgetUpdateWorker { *; }
-keep class so.appio.app.widgets.WidgetButtonAction { *; }

# Keep all Glance-related classes and their members
-keep class * extends androidx.glance.appwidget.GlanceAppWidget { *; }
-keep class * extends androidx.glance.appwidget.GlanceAppWidgetReceiver { *; }
-keep class * implements androidx.glance.appwidget.action.ActionCallback { *; }

# Keep Glance state and preferences
-keep class androidx.glance.** { *; }
-keepclassmembers class * {
    @androidx.glance.appwidget.action.ActionCallback *;
}

# ===============================================================================
# DATA CLASSES & MODELS
# ===============================================================================
# Keep all data classes and their fields (for serialization/DataStore)
-keep class so.appio.app.utils.DeviceInfoManager$DeviceInfo { *; }
-keepclassmembers class so.appio.app.utils.DeviceInfoManager$DeviceInfo {
    <fields>;
    <init>(...);
}

# ===============================================================================
# ROOM DATABASE
# ===============================================================================
# Keep Room entities and their fields
-keep class so.appio.app.data.entity.** { *; }
-keepclassmembers class so.appio.app.data.entity.** {
    <fields>;
    <init>(...);
}

# Keep Room DAOs
-keep interface so.appio.app.data.dao.** { *; }

# Keep Room database classes
-keep class so.appio.app.data.database.** { *; }

# Keep Room type converters
-keep class so.appio.app.data.converters.** { *; }

# Keep DataStore classes
-keep class so.appio.app.data.** { *; }

# ===============================================================================
# ANDROID FRAMEWORK
# ===============================================================================
# For Parcelable implementations
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator *;
}

# Keep constructors for reflection
-keepclassmembers class * {
    public <init>(...);
}

# ===============================================================================
# JETPACK COMPOSE
# ===============================================================================
# Keep Compose runtime classes
-keep class androidx.compose.runtime.** { *; }

# Keep Compose UI classes that might be accessed via reflection
-keep class androidx.compose.ui.platform.** { *; }
-keep class androidx.compose.ui.tooling.** { *; }

# Keep preview annotations for debug builds
-keep class androidx.compose.ui.tooling.preview.** { *; }

# ===============================================================================
# FIREBASE
# ===============================================================================
# Keep Firebase Messaging classes
-keep class com.google.firebase.messaging.** { *; }
-keep class com.google.firebase.iid.** { *; }

# Keep Firebase service implementations
-keep class so.appio.app.utils.FirebaseMessagingService { *; }

# Keep Firebase model classes
#-keepclassmembers class * {
#    @com.google.firebase.database.PropertyName <fields>;
#    @com.google.firebase.database.PropertyName <methods>;
#}

# ===============================================================================
# GLIDE IMAGE LOADING
# ===============================================================================
# Keep Glide classes
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
    <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
    **[] $VALUES;
    public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
    *** rewind();
}

# OkHttp for Glide integration
-dontwarn okhttp3.**
-dontwarn okio.**
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }

# Suppress warnings for Conscrypt (used by OkHttp)
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**

# OkHttp platform-specific warnings
-dontwarn okhttp3.internal.platform.**
-dontwarn org.conscrypt.ConscryptEngineSocket

# ===============================================================================
# ANDROIDX LIBRARIES
# ===============================================================================
# WorkManager
-keep class androidx.work.** { *; }
-keep class * extends androidx.work.Worker
-keep class * extends androidx.work.InputMerger
-keepclassmembers class * extends androidx.work.Worker {
    public <init>(android.content.Context,androidx.work.WorkerParameters);
}

# Navigation Component
-keep class androidx.navigation.** { *; }

# DataStore
-keep class androidx.datastore.** { *; }
-keepclassmembers class * extends androidx.datastore.core.Serializer {
    public <init>(...);
}

# ===============================================================================
# INSTALL REFERRER
# ===============================================================================
-keep class com.android.installreferrer.** { *; }

# ===============================================================================
# KOTLIN & COROUTINES
# ===============================================================================
# Keep Kotlin metadata
-keepattributes *Annotation*

# Coroutines
-keepclassmembers class kotlinx.coroutines.** {
    volatile <fields>;
}
-keep class kotlinx.coroutines.** { *; }

# Keep Kotlin serialization if used
-keepattributes RuntimeVisibleAnnotations,RuntimeVisibleParameterAnnotations,RuntimeVisibleTypeAnnotations

# ===============================================================================
# GENERAL OPTIMIZATIONS
# ===============================================================================
# Remove logging in release builds (optional - uncomment if desired)
# -assumenosideeffects class android.util.Log {
#     public static boolean isLoggable(java.lang.String, int);
#     public static int v(...);
#     public static int i(...);
#     public static int w(...);
#     public static int d(...);
#     public static int e(...);
# }

# Keep enum values
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# ===============================================================================
# WARNINGS SUPPRESSION
# ===============================================================================
# Additional warnings suppression (main ones are handled in OkHttp section above)