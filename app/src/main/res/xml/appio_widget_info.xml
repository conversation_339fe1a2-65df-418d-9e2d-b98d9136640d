<?xml version="1.0" encoding="utf-8"?>
<appwidget-provider
    xmlns:android="http://schemas.android.com/apk/res/android"

    android:description="@string/widget_description"
    android:widgetCategory="home_screen|keyguard"

    android:updatePeriodMillis="1800000"

    android:resizeMode="horizontal|vertical"
    android:minWidth="40dp"
    android:minHeight="40dp"
    android:targetCellWidth="2"
    android:targetCellHeight="1"

    android:widgetFeatures="reconfigurable"

    android:configure="so.appio.app.widgets.AppioWidgetConfigActivity"

    android:initialLayout="@layout/widget_initial_layout"
    android:previewImage="@drawable/widget_preview"
    android:previewLayout="@layout/widget_preview_layout"
    />

<!--
    NOTES:
    - min value for `updatePeriodMillis` is 30 minutes = 1800000 milliseconds
      https://developer.android.com/reference/android/appwidget/AppWidgetProviderInfo#updatePeriodMillis
    - values for minWidth, minHeight calculation. but targetCellWidth, targetCellHeight settings have preference
      minWidth = 70dp * targetCellWidth - 30dp
      minHeight = 70dp * targetCellHeight - 30dp
    - android:widgetFeatures="configuration_optional|reconfigurable"
      configuration_optional = widget can be added without configuration
      reconfigurable = widget can be reconfigured after being added
    - android:previewLayout is deprecated in API 31: https://developer.android.com/develop/ui/views/appwidgets#preview
-->