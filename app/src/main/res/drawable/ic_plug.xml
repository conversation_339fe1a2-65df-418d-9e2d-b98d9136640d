<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="96dp"
    android:height="34dp"
    android:viewportWidth="1424"
    android:viewportHeight="494">
    
    <!-- Main plug cable paths -->
    <path
        android:pathData="M611.043,152.704C618.849,151.959 622.761,298.525 613.844,322.668C582.609,407.245 444.29,354.047 417.496,290.573C396.375,240.539 384.657,167.734 409.371,117.094C433.394,67.8677 482.205,37.7926 533.892,28.0076C568.716,21.4152 597.345,35.0778 601.898,70.5221C603.573,83.5641 605.876,96.0605 606.93,109.214C608.731,131.668 618.692,149.834 620.875,171.712"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <!-- Horizontal connector lines -->
    <path
        android:pathData="M604.699,115.77C649.351,115.77 694.195,113.918 738.898,106.807"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M624.152,256.768C584.61,261.8 703.868,255.444 743.73,255.444"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <!-- Left side cable -->
    <path
        android:pathData="M389.408,217.428C409.907,217.428 348.487,214.36 327.988,214.36C284.449,214.36 256.055,233.615 231.433,267.054C207.984,298.898 220.035,355.958 213.707,393.304C208.402,424.609 186.459,450.356 157.727,464.25C136.359,474.584 110.375,480.031 87.1447,484.331C59.7038,489.411 34.3221,478.332 8.02844,472.114"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <!-- Right side cable -->
    <path
        android:pathData="M828.316,202.843C828.708,202.869 828.988,300.73 828.988,314.312C828.988,341.984 831.45,363.717 856.733,378.725C887.578,397.035 924.547,368.165 951.456,355.239C990.541,336.464 1017.52,295.504 1025.88,253.184C1030.85,228.057 1035.96,205.666 1035.96,179.838C1035.96,158.934 1031.09,138.726 1028.69,118.068C1026.19,96.6504 1025.23,78.0119 1008.86,62.0389C991.805,45.3948 970.746,21.0937 947.002,15.7983C916.316,8.95467 860.862,-3.19483 842.088,31.1071C828.242,56.4043 828.413,88.1893 827.445,116.454C826.467,145.025 821.632,187.018 832.896,213.309"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <!-- Right extension -->
    <path
        android:pathData="M1035.96,176.797C1075.67,176.797 1115.37,176.629 1154.53,186C1183.21,192.864 1285.44,228.175 1249.32,276.095C1239.02,289.764 1214.32,304.211 1197.68,293.359C1181.29,282.668 1179.36,260.627 1198.98,254.146C1239.44,240.788 1285.71,251.661 1317.68,279.594C1343.29,301.976 1361.33,335.713 1382.9,362.09C1396.17,378.305 1411.69,395.852 1415.83,416.865"
        android:strokeColor="#000000"
        android:strokeWidth="16"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <!-- Detail lines - left side -->
    <path
        android:pathData="M409.43,133.274C417.38,133.274 430.877,115.262 436.587,110.534"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M397.363,165.681C397.363,162.238 401.997,159.686 404.298,157.662C415.824,147.53 427.242,137.264 439.2,127.642"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M400.563,192.246C385.351,201.379 424.933,166.413 437.967,154.375"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M402.53,219.147C414.84,200.005 440.828,183.987 458.284,170.725"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M417.357,240.542C417.714,242.703 416.998,246.21 419.249,243.681C429.818,231.802 442.505,221.849 454.79,211.834"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M415.067,279.044C419.761,277.862 422.957,272.495 425.972,269.041C438.603,254.571 452.889,241.502 466.534,228.016"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M436.441,293.714C437.562,296.579 464.545,270.304 465.918,268.956"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M454.79,321.792C462.312,309.717 474.948,301.213 485.618,292.159"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />
    
    <path
        android:pathData="M474.491,335.873C478.983,329.36 484.568,324.599 490.668,319.522"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <!-- Detail lines - right side -->
    <path
        android:pathData="M840.737,63.7899C838.59,66.9895 834.641,67.9159 839.504,64.2265C850.303,56.0325 862.225,49.22 873.737,42.1008"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M827.907,102.754C828.114,104.829 833.105,100.614 833.234,100.517C848.026,89.4203 862.779,78.2731 877.554,67.1525"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M828.817,141.257C828.817,138.316 832.786,136.545 834.8,135.195C849.766,125.155 865.058,115.629 879.991,105.529"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M830.813,174.883C832.123,174.55 836.81,168.67 838.052,167.635C852.873,155.281 868.015,143.289 882.897,131.001"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M828.523,209.855C831.278,209.739 835.908,204.476 837.998,203.015C851.655,193.468 866.096,185.061 880.138,176.102"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M825.763,241.926C826.602,241.926 831.16,236.5 832.014,235.864C847.735,224.144 864.62,213.874 880.901,202.961"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M829.903,279.671C832.979,279.671 835.707,275.241 837.833,273.348C853.365,259.515 871.026,247.948 887.771,235.663"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M825,311.28C829.208,318.942 883.207,271.425 888.094,267.44"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M840.443,343.688C843.645,337.788 849.229,334.505 854.582,330.695C870.872,319.099 886.021,306.041 901.365,293.248"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M854.184,366.764C857.579,367.979 861.524,362.72 863.584,360.937C881.484,345.442 900.025,330.836 918.628,316.198"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

    <path
        android:pathData="M899.075,366.133C899.047,366.137 903.395,363.099 904.441,362.336C915.849,354.013 926.884,345.213 937.859,336.332"
        android:strokeColor="#000000"
        android:strokeWidth="4"
        android:strokeLineCap="round"
        android:strokeLineJoin="round"
        android:fillColor="@android:color/transparent" />

</vector>
