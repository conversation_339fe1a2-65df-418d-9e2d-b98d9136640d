<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.Appio" parent="android:Theme.Material.NoActionBar">
        <!-- Optional: customize system UI like status bar color -->
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>

    <!-- This is the splash theme -->
    <style name="Theme.Appio.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">@color/ic_launcher_background</item>
<!--        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>-->
        <item name="windowSplashScreenAnimatedIcon">@drawable/ic_splash</item>
        <item name="windowSplashScreenAnimationDuration">0</item> <!-- 0 to disable icon animation -->
        <item name="postSplashScreenTheme">@style/Theme.Appio</item>
    </style>
</resources>
