<resources>
    <string name="app_name">Appio</string>

    <!-- Default FCM channel, fallback. Each service has its own channel generated in dynamically -->
    <string name="default_notification_channel_id">fcm_default_channel</string>

    <!-- Widget config -->
    <string name="widget_description">Appio Widget</string>
    <string name="widget_error">Error displaying the widget. Please remove it and re-add it.</string>
    <string name="widget_preview_widget">Widget icon</string>
    <string name="widget_preview_hand">Dragging hand</string>
    <string name="widget_preview_phone">Phone icon</string>
    <string name="widget_preview_arrow">Dragging arrow indicator</string>

    <!-- Notification -->
    <string name="notification_heads_up_logo_description">Service Logo</string>
    <string name="notification_heads_up_title">Message Title</string>
    <string name="notification_heads_up_body">Message body</string>

</resources>