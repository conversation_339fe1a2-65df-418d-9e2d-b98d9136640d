<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/widget_preview_background"
    android:gravity="center"
    android:orientation="horizontal"
    android:padding="16dp">

    <ImageView
        android:id="@+id/widget_preview_widget"
        android:contentDescription="@string/widget_preview_widget"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginEnd="8dp"
        android:src="@drawable/widget_widget"
        android:tint="@color/widget_preview_tint"
        tools:ignore="UseAppTint" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginHorizontal="8dp"
        android:gravity="center"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/widget_preview_arrow"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:layout_marginBottom="4dp"
            android:contentDescription="@string/widget_preview_arrow"
            android:src="@drawable/widget_arrow"
            android:tint="@color/widget_preview_tint"
            tools:ignore="UseAppTint" />

        <ImageView
            android:id="@+id/widget_preview_hand"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:contentDescription="@string/widget_preview_hand"
            android:scaleType="fitCenter"
            android:src="@drawable/widget_hand"
            android:tint="@color/widget_preview_tint"
            tools:ignore="UseAppTint" />
    </LinearLayout>

    <ImageView
        android:id="@+id/widget_preview_phone"
        android:contentDescription="@string/widget_preview_phone"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        android:src="@drawable/widget_phone"
        android:tint="@color/widget_preview_tint"
        tools:ignore="UseAppTint" />

</LinearLayout>