package so.appio.app.glide

import android.content.Context
import com.bumptech.glide.Glide
import com.bumptech.glide.Registry
import com.bumptech.glide.annotation.GlideModule
import com.bumptech.glide.integration.okhttp3.OkHttpUrlLoader
import com.bumptech.glide.load.model.GlideUrl
import com.bumptech.glide.module.AppGlideModule
import so.appio.app.network.HttpClientConfig
import java.io.InputStream

/**
 * Auto loaded by `Glide.with(context)`
 * Custom Glide module for configuring image loading behavior
 * This resolves the "Failed to find GeneratedAppGlideModule" error
 */
@GlideModule
class AppGlideModule : AppGlideModule() {

    override fun registerComponents(context: Context, glide: Glide, registry: Registry) {
        // Use shared HTTP client configuration for consistency
        val client = HttpClientConfig.createClient()

        // Replace the default HTTP loader with our configured OkHttp loader
        registry.replace(
            GlideUrl::class.java,
            InputStream::class.java,
            OkHttpUrlLoader.Factory(client)
        )
    }

    override fun isManifestParsingEnabled(): Boolean {
        // Disable manifest parsing for better performance
        return false
    }
}
