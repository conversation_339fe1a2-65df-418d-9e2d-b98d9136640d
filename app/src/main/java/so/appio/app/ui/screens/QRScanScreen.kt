package so.appio.app.ui.screens

import android.Manifest
import android.content.pm.PackageManager
import android.util.Log
import so.appio.app.BuildConfig
import so.appio.app.utils.BrowserUtils
import so.appio.app.utils.UrlValidator
import so.appio.app.utils.UrlValidationResult
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.OpenInNew
import androidx.compose.material.icons.filled.FlashOn
import androidx.compose.material.icons.filled.FlashOff
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanning
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import so.appio.app.ui.theme.AppioAppTheme
import java.util.concurrent.Executors
import androidx.camera.core.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import android.content.res.Configuration
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalLayoutDirection

// QR Scanner UI Constants
private const val URL_LABEL_AUTO_HIDE_DELAY_MS = 1000L
private val SCANNING_OVERLAY_SIZE = 250.dp
private val URL_LABEL_SPACING_FROM_OVERLAY = 50.dp

enum class CameraPermissionState {
    UNKNOWN,    // Permission not requested yet
    REQUESTING, // Permission request is in progress
    DENIED,     // Permission was requested and denied
    GRANTED     // Permission was granted
}

@Composable
fun QRScanScreen(
    modifier: Modifier = Modifier,
    orientation: Int,
    onBackClick: () -> Unit,
    onValidAppioUrl: ((String) -> Unit),
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // URL label state
    var showUrlLabel by remember { mutableStateOf(false) }
    var urlLabelText by remember { mutableStateOf("") }
    var urlToOpen by remember { mutableStateOf("") }
    var hideUrlJob by remember { mutableStateOf<Job?>(null) }

    // Function to handle QR code detection and validation
    val handleQRCodeDetected: (String) -> Unit = { qrCode ->
        Log.d(MainViewModel.TAG_UI, "QR Code detected: $qrCode")

        // First, hide any existing label and cancel any pending hide timer
        hideUrlJob?.cancel()
        showUrlLabel = false

        // Validate and parse the QR code URL
        when (val result = UrlValidator.validateAndParseUrl(qrCode)) {
            is UrlValidationResult.Success -> {
                Log.d(MainViewModel.TAG_UI, "Valid Appio URL - serviceId: ${result.params.serviceId}, customerUserId: ${result.params.customerUserId}")
                onValidAppioUrl(qrCode)
            }
            is UrlValidationResult.Error -> {
                Log.w(MainViewModel.TAG_UI, "Invalid QR code URL: ${result.message}")

                // Check if it's a valid URL that can be opened in a browser
                if (UrlValidator.isValidBrowserUrl(qrCode)) {
                    val mainDomain = UrlValidator.extractMainDomain(qrCode)
                    if (mainDomain != null) {
                        Log.d(MainViewModel.TAG_UI, "Valid browser URL detected: $mainDomain")
                        urlLabelText = mainDomain
                        urlToOpen = qrCode
                        showUrlLabel = true

                        // Set up auto-hide timer
                        hideUrlJob = CoroutineScope(Dispatchers.Main).launch {
                            delay(URL_LABEL_AUTO_HIDE_DELAY_MS)
                            showUrlLabel = false
                        }
                    }
                }
            }
        }
    }

    // Track permission state
    var permissionState by remember { mutableStateOf(CameraPermissionState.UNKNOWN) }

    // State to trigger permission re-check when app resumes
    var permissionCheckTrigger by remember { mutableStateOf(0) }

    // Check current permission status (re-evaluated when permissionCheckTrigger changes)
    val hasCameraPermission = remember(permissionCheckTrigger) {
        ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    // Flashlight state management
    var isFlashlightOn by remember { mutableStateOf(false) }
    var hasFlashlight by remember { mutableStateOf(false) }
    var camera by remember { mutableStateOf<Camera?>(null) }

    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        permissionState = if (isGranted) {
            CameraPermissionState.GRANTED
        } else {
            CameraPermissionState.DENIED
        }
    }

    // Check if running in debug build
    val isDebug = remember { BuildConfig.IS_DEBUG }

    // Check if device has flashlight
    LaunchedEffect(Unit) {
        hasFlashlight = context.packageManager.hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH)
    }

    // Listen for app lifecycle changes to re-check permissions when app resumes
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // Trigger permission re-check when app resumes
                permissionCheckTrigger++
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // Request permission immediately when screen appears
    LaunchedEffect(Unit) {
        if (!hasCameraPermission /*&& !isCameraPermissionRequested*/) {
            permissionState = CameraPermissionState.REQUESTING
            launcher.launch(Manifest.permission.CAMERA)
        }
    }

    // Update permission state based on current permission and request history
    LaunchedEffect(hasCameraPermission) { //, isCameraPermissionRequested) {
        // Only update state if not currently requesting (to avoid overriding REQUESTING state)
        if (permissionState != CameraPermissionState.REQUESTING) {
            permissionState = when {
                hasCameraPermission -> CameraPermissionState.GRANTED
                // isCameraPermissionRequested -> CameraPermissionState.DENIED
                else -> CameraPermissionState.DENIED // else -> CameraPermissionState.UNKNOWN
            }
        }
    }

    // Function to toggle flashlight
    val toggleFlashlight: () -> Unit = {
        camera?.let { cam ->
            if (cam.cameraInfo.hasFlashUnit()) {
                isFlashlightOn = !isFlashlightOn
                cam.cameraControl.enableTorch(isFlashlightOn)
            }
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Show camera preview only when permission is granted
        if (permissionState == CameraPermissionState.GRANTED) {
            CameraPreview(
                onQRCodeDetected = handleQRCodeDetected,
                onCameraReady = { cam ->
                    camera = cam
                },
                lifecycleOwner = lifecycleOwner,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Top bar with back button and centered title (only show when not in unknown or requesting state)
        if (permissionState != CameraPermissionState.UNKNOWN && permissionState != CameraPermissionState.REQUESTING) {
            if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            vertical = 32.dp,
                            horizontal = 16.dp + WindowInsets.navigationBars.asPaddingValues().calculateStartPadding(LocalLayoutDirection.current)
                        )
                ) {
                    // Back button positioned at start
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.align(Alignment.CenterStart)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.White
                        )
                    }
                }
            } else {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                        .statusBarsPadding()
                ) {
                    // Back button positioned at start
                    IconButton(
                        onClick = onBackClick,
                        modifier = Modifier.align(Alignment.CenterStart)
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.White
                        )
                    }

                    // Centered title text
                    Text(
                        text = "Find a QR code to scan",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = Color.White,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.align(Alignment.Center)
                    )
                }
            }
        }

        if (permissionState == CameraPermissionState.GRANTED) {
            // QR Code scanning frame overlay with pulse animation
            val infiniteTransition = rememberInfiniteTransition(label = "pulse")
            val scale by infiniteTransition.animateFloat(
                initialValue = 1f,
                targetValue = 1.1f,
                animationSpec = infiniteRepeatable(
                    animation = keyframes {
                        durationMillis = 1200 // Faster overall animation
                        1f at 0 using EaseInOut
                        1.1f at 800 using EaseInOut // Slower expansion (800ms)
                        1f at 1200 using EaseInOut // Faster contraction (400ms = 3x faster)
                    },
                    repeatMode = RepeatMode.Restart
                ),
                label = "scale"
            )

            Box(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(SCANNING_OVERLAY_SIZE)
                    .scale(scale)
                    .drawBehind {
                        val strokeWidth = 6.dp.toPx()
                        val cornerLength = 40.dp.toPx()
                        val cornerRadius = 20.dp.toPx()

                        // Draw curved corner indicators (no square outline)
                        val indicatorStroke = Stroke(
                            width = strokeWidth,
                            cap = StrokeCap.Round
                        )

                        // Top-left corner - curved
                        drawArc(
                            color = Color.White,
                            startAngle = 180f,
                            sweepAngle = 90f,
                            useCenter = false,
                            topLeft = Offset(0f, 0f),
                            size = Size(
                                cornerRadius * 2,
                                cornerRadius * 2
                            ),
                            style = indicatorStroke
                        )
                        // Top-left vertical line
                        drawLine(
                            color = Color.White,
                            start = Offset(0f, cornerRadius),
                            end = Offset(0f, cornerLength),
                            strokeWidth = strokeWidth
                        )
                        // Top-left horizontal line
                        drawLine(
                            color = Color.White,
                            start = Offset(cornerRadius, 0f),
                            end = Offset(cornerLength, 0f),
                            strokeWidth = strokeWidth
                        )

                        // Top-right corner - curved
                        drawArc(
                            color = Color.White,
                            startAngle = 270f,
                            sweepAngle = 90f,
                            useCenter = false,
                            topLeft = Offset(size.width - cornerRadius * 2, 0f),
                            size = androidx.compose.ui.geometry.Size(
                                cornerRadius * 2,
                                cornerRadius * 2
                            ),
                            style = indicatorStroke
                        )
                        // Top-right vertical line
                        drawLine(
                            color = Color.White,
                            start = Offset(size.width, cornerRadius),
                            end = Offset(size.width, cornerLength),
                            strokeWidth = strokeWidth
                        )
                        // Top-right horizontal line
                        drawLine(
                            color = Color.White,
                            start = Offset(size.width - cornerRadius, 0f),
                            end = Offset(size.width - cornerLength, 0f),
                            strokeWidth = strokeWidth
                        )

                        // Bottom-left corner - curved
                        drawArc(
                            color = Color.White,
                            startAngle = 90f,
                            sweepAngle = 90f,
                            useCenter = false,
                            topLeft = Offset(0f, size.height - cornerRadius * 2),
                            size = androidx.compose.ui.geometry.Size(
                                cornerRadius * 2,
                                cornerRadius * 2
                            ),
                            style = indicatorStroke
                        )
                        // Bottom-left vertical line
                        drawLine(
                            color = Color.White,
                            start = Offset(0f, size.height - cornerRadius),
                            end = Offset(0f, size.height - cornerLength),
                            strokeWidth = strokeWidth
                        )
                        // Bottom-left horizontal line
                        drawLine(
                            color = Color.White,
                            start = Offset(cornerRadius, size.height),
                            end = Offset(cornerLength, size.height),
                            strokeWidth = strokeWidth
                        )

                        // Bottom-right corner - curved
                        drawArc(
                            color = Color.White,
                            startAngle = 0f,
                            sweepAngle = 90f,
                            useCenter = false,
                            topLeft = Offset(
                                size.width - cornerRadius * 2,
                                size.height - cornerRadius * 2
                            ),
                            size = androidx.compose.ui.geometry.Size(
                                cornerRadius * 2,
                                cornerRadius * 2
                            ),
                            style = indicatorStroke
                        )
                        // Bottom-right vertical line
                        drawLine(
                            color = Color.White,
                            start = Offset(size.width, size.height - cornerRadius),
                            end = Offset(size.width, size.height - cornerLength),
                            strokeWidth = strokeWidth
                        )
                        // Bottom-right horizontal line
                        drawLine(
                            color = Color.White,
                            start = Offset(size.width - cornerRadius, size.height),
                            end = Offset(size.width - cornerLength, size.height),
                            strokeWidth = strokeWidth
                        )
                    }
            ) {
                // Debug buttons for debug builds only
                if (isDebug) {
                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Button(
                            onClick = {
                                val testUrl = "https://app.appio.so/?s=svc_00dddddd000000ccccccssssss&u=qr-emulator"
                                handleQRCodeDetected(testUrl)
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF4CAF50)
                            ),
                            elevation = ButtonDefaults.buttonElevation(
                                defaultElevation = 8.dp
                            )
                        ) {
                            Text(
                                text = "Valid Appio",
                                color = Color.White,
                                fontWeight = FontWeight.Medium,
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }

            // URL label below the scanning overlay (when a valid browser URL is detected)
            if (showUrlLabel) {
                // Calculate position based on scanning overlay base size (without scale animation)
                val overlayBottomOffset = (SCANNING_OVERLAY_SIZE / 2) + URL_LABEL_SPACING_FROM_OVERLAY

                Box(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .offset(y = overlayBottomOffset)
                        .background(
                            color = Color.Black.copy(alpha = 0.8f),
                            shape = RoundedCornerShape(12.dp)
                        )
                        .clickable {
                            // Open URL in browser when clicked
                            if (BrowserUtils.openUrl(context, urlToOpen)) {
                                Log.d(MainViewModel.TAG_UI, "Successfully opened URL: $urlToOpen")
                            } else {
                                Log.e(MainViewModel.TAG_UI, "Failed to open URL: $urlToOpen")
                            }
                        }
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = urlLabelText,
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            textAlign = TextAlign.Center
                        )

                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.OpenInNew,
                            contentDescription = "Open link",
                            tint = Color.White.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            // Flashlight button at bottom (only show if device has flashlight or debug build)
            if (hasFlashlight || isDebug) {
                FloatingActionButton(
                    onClick = toggleFlashlight,
                    modifier = Modifier
                        .align(
                            if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                                Alignment.TopEnd
                            } else {
                                Alignment.BottomCenter
                            }
                        )
                        .padding(
                            horizontal = 32.dp + WindowInsets.navigationBars.asPaddingValues().calculateEndPadding(LocalLayoutDirection.current),
                            vertical = 32.dp + WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding()
                        ),
                    containerColor = if (isFlashlightOn) Color.White else Color.Black,
                    contentColor = if (isFlashlightOn) Color.Black else Color.White
                ) {
                    Icon(
                        imageVector = if (isFlashlightOn) Icons.Default.FlashOff else Icons.Default.FlashOn,
                        contentDescription = if (isFlashlightOn) "Turn off flashlight" else "Turn on flashlight"
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Permission unknown/requesting state - show nothing (permission request happens automatically)

        // Permission denied overlay (only show when explicitly denied, not when unknown or requesting)
        if (permissionState == CameraPermissionState.DENIED) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                contentAlignment = Alignment.Center,
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "Camera Permission Required",
                        style = MaterialTheme.typography.headlineSmall.copy(
                            fontWeight = FontWeight.Bold
                        ),
                        color = Color.White,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "To scan QR codes, please grant camera permission.",
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.White.copy(alpha = 0.8f),
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Button(
                        onClick = {
                            BrowserUtils.openAppCameraSettings(context)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary
                        )
                    ) {
                        Text("Grant Permission")
                    }

                    if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                        Spacer(modifier = Modifier.height(8.dp))

                        OutlinedButton(
                            onClick = onBackClick,
//                            colors = ButtonDefaults.buttonColors(
//                                containerColor = MaterialTheme.colorScheme.primary
//                            )
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = Color.White,
                                modifier = Modifier.size(16.dp)
                            )

                            Spacer(modifier = Modifier.width(8.dp))

                            Text(
                                text = "Back",
                                color = Color.White,
//                                fontSize = 22.sp,
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun CameraPreview(
    onQRCodeDetected: (String) -> Unit,
    onCameraReady: (Camera) -> Unit,
    lifecycleOwner: LifecycleOwner,
    modifier: Modifier = Modifier
) {
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }

    DisposableEffect(Unit) {
        onDispose {
            cameraExecutor.shutdown()
        }
    }

    AndroidView(
        factory = { ctx ->
            val previewView = PreviewView(ctx)
            val cameraProviderFuture = ProcessCameraProvider.getInstance(ctx)

            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()

                val preview = androidx.camera.core.Preview.Builder().build().also {
                    it.surfaceProvider = previewView.surfaceProvider
                }

                val imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor, QRCodeAnalyzer(onQRCodeDetected))
                    }

                val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA

                try {
                    cameraProvider.unbindAll()
                    val camera = cameraProvider.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageAnalyzer
                    )

                    // Notify that camera is ready
                    onCameraReady(camera)
                } catch (exc: Exception) {
                    Log.e(MainViewModel.TAG_UI, "Use case binding failed", exc)
                }

            }, ContextCompat.getMainExecutor(ctx))

            previewView
        },
        modifier = modifier
    )
}

private class QRCodeAnalyzer(
    private val onQRCodeDetected: (String) -> Unit
) : ImageAnalysis.Analyzer {
    
    private val scanner = BarcodeScanning.getClient()
    
    @ExperimentalGetImage
    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
            
            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    for (barcode in barcodes) {
                        when (barcode.valueType) {
                            Barcode.TYPE_TEXT,
                            Barcode.TYPE_URL -> {
                                barcode.rawValue?.let { value ->
                                    onQRCodeDetected(value)
                                }
                            }
                        }
                    }
                }
                .addOnFailureListener { exception ->
                    Log.e(MainViewModel.TAG_UI, "Barcode scanning failed", exception)
                    so.appio.app.utils.SentryErrorHandler.reportError(
                        exception = exception,
                        component = "QRScanScreen",
                        operation = "barcode_scanning"
                    )
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        } else {
            imageProxy.close()
        }
    }
}

@Preview(showBackground = true)
@Composable
fun QRScanScreenPreviewPortrait() {
    AppioAppTheme {
        QRScanScreen(
            orientation = android.content.res.Configuration.ORIENTATION_PORTRAIT,
            onBackClick = {},
            onValidAppioUrl = {  },
        )
    }
}

@Preview(showBackground = true, widthDp = 720, heightDp = 360,)
@Composable
fun QRScanScreenPreviewLandscape() {
    AppioAppTheme {
        QRScanScreen(
            orientation = android.content.res.Configuration.ORIENTATION_LANDSCAPE,
            onBackClick = {},
            onValidAppioUrl = {  },
        )
    }
}
