package so.appio.app.ui.screens

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import so.appio.app.data.AppDataStore
import so.appio.app.utils.IntentHandler
import so.appio.app.utils.InstallReferrer
import so.appio.app.utils.StartupUrlProcessor
import so.appio.app.widgets.AppioWidget
import androidx.glance.appwidget.updateAll
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.utils.FingerPrintManager
import so.appio.app.utils.FeatureFlagManager
import so.appio.app.utils.SentryErrorHandler
import so.appio.app.data.database.DatabaseManager
import kotlin.coroutines.cancellation.CancellationException

class MainViewModel : ViewModel() {
    companion object {
        const val TAG = "LOG:MainViewModel"
        const val TAG_UI = "LOG:UI:Screens"
    }

    private val _isSplashScreenLoading = MutableStateFlow(true)
    val isSplashScreenLoading: StateFlow<Boolean> = _isSplashScreenLoading

    private val _isLoading = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading

    // Services state - will be initialized after DatabaseManager is ready
    private val _services = MutableStateFlow<List<Service>>(emptyList())
    val services: StateFlow<List<Service>> = _services

    // Selected service ID state (we store ID instead of object to avoid stale data)
    private val _selectedServiceId = MutableStateFlow<String?>(null)

    // Current notification state for navigation to NotificationScreen
    private val _currentNotification = MutableStateFlow<Notification?>(null)
    val currentNotification: StateFlow<Notification?> = _currentNotification

    // ServiceViewModel cache to preserve ViewModels across navigation
    private val serviceViewModels = mutableMapOf<String, ServiceViewModel>()

    // Computed current service state - always gets fresh data from services flow
    val currentService: StateFlow<Service?> = combine(services, _selectedServiceId) { servicesList, selectedId ->
        selectedId?.let { id -> servicesList.find { it.id == id } }
            ?: if (servicesList.size == 1) servicesList.first() else null
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(), null)

    // Dependencies
    private lateinit var appDataStore: AppDataStore
    private lateinit var appContext: Context
    private lateinit var fingerPrintManager: FingerPrintManager
    private lateinit var featureFlagManager: FeatureFlagManager
    private var isInitialized = false

    fun getContext(): Context {
        return appContext
    }

    fun initialize(context: Context) {
        try {
            appContext = context
            appDataStore = AppDataStore(context)
            fingerPrintManager = FingerPrintManager(context)
            featureFlagManager = FeatureFlagManager(context)
            isInitialized = true

            // Set up reactive services flow from database
            setupDatabaseServicesFlowObserver()

            Log.d(TAG, "MainViewModel initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize MainViewModel", e)
            SentryErrorHandler.reportError(
                exception = e,
                component = "MainViewModel",
                operation = "initialize"
            )
            isInitialized = false
        }
    }

    /**
     * Set up reactive flow for services from database
     */
    private fun setupDatabaseServicesFlowObserver() {
        if (!DatabaseManager.isInitialized()) {
            Log.w(TAG, "DatabaseManager not initialized, cannot set up services flow")
            return
        }

        viewModelScope.launch {
            try {
                // This will emit the updated list every time the table changes.
                DatabaseManager.getServiceRepository().getAllServices()
                    .collect { servicesList ->
                        _services.value = servicesList
                        Log.d(TAG, "Services updated from database: ${servicesList.size} services")
                    }
            } catch (_: CancellationException) {
                // Ignore cancellation exceptions since we're using stateIn
                Log.e(TAG, "Services flow cancelled")
            } catch (e: Exception) {
                Log.e(TAG, "Error setting up services flow", e)
                SentryErrorHandler.reportError(
                    exception = e,
                    component = "MainViewModel",
                    operation = "setup_services_flow"
                )
            }
        }
    }

    /**
     * Load feature flags during splash screen initialization.
     * This ensures feature flags are available for all intent types.
     */
    fun loadFeatureFlags() {
        if (!ensureInitialized()) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "Loading feature flags during splash screen...")
                featureFlagManager.check()
                Log.d(TAG, "Feature flags loaded successfully")
            } catch (e: Exception) {
                Log.e(TAG, "Error loading feature flags", e)
            } finally {
                // Dismiss splash screen after feature flags are loaded (or failed)
                Log.d(TAG, "Dismissing splash screen")
                _isSplashScreenLoading.value = false
            }
        }
    }

    /**
     * Check if the ViewModel is properly initialized
     * @return true if initialized, false otherwise
     */
    private fun ensureInitialized(): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "MainViewModel not initialized - call initialize() first")
            return false
        }
        return true
    }

    fun handleIntent(intent: Intent) {
        if (!ensureInitialized()) return

        val intentSource = IntentHandler.detectIntentSource(intent)
        Log.d(TAG, "Intent: $intentSource")

        // Start loading screen. Every intent has to cancel this
        _isLoading.value = true

        // Always detect services first, then let intents override if needed
        viewModelScope.launch {
            // Note: Splash screen will be dismissed by loadFeatureFlags() after feature flags are loaded
            // This ensures feature flags are available for all intent types

            // Delegate to appropriate intent handlers
            when (intentSource) {
                IntentHandler.IntentSource.NOTIFICATION -> IntentHandler.handleNotificationIntent(intent, this@MainViewModel)
                IntentHandler.IntentSource.WIDGET -> IntentHandler.handleWidgetIntent(intent, this@MainViewModel)
                IntentHandler.IntentSource.SHORTCUT -> IntentHandler.handleShortcutIntent(intent, this@MainViewModel)
                IntentHandler.IntentSource.DEEP_LINK -> IntentHandler.handleDeepLinkIntent(intent, this@MainViewModel)
                IntentHandler.IntentSource.NORMAL_LAUNCH -> handleNormalLaunch()
                IntentHandler.IntentSource.OTHER -> IntentHandler.handleOtherIntent(intent, this@MainViewModel)
            }
        }
    }

    fun intentDone() {
        _isLoading.value = false
    }

    /**
     * Handle normal app launch - delegates to FingerPrintManager for complete logic
     */
    private fun handleNormalLaunch() {
        Log.d(TAG, "Handling normal launch intent")

        viewModelScope.launch {
            fingerPrintManager.handleNormalLaunch(
                onServiceFound = { service ->
                    navigateToService(service)
                },
                onComplete = {
                    intentDone()
                }
            )
        }
    }

    /**
     * Navigate to service from intent with specific service ID
     */
    fun navigateToServiceById(serviceId: String) {
        if (!ensureInitialized()) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "Navigating to service by ID: $serviceId")

                if (!DatabaseManager.isInitialized()) {
                    Log.w(TAG, "DatabaseManager not initialized, cannot navigate to service")
                    return@launch
                }

                // NOTE: can't use `self.service` as it is not populated at this point. due to race condition.
                val serviceRepository = DatabaseManager.getServiceRepository()
                val service = serviceRepository.getServiceById(serviceId)

                if (service != null) {
                    Log.d(TAG, "Found service: ${service.title}")
                    navigateToService(service)
                } else {
                    Log.w(TAG, "Service not found by ID: $serviceId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error navigating to service from intent with ID $serviceId", e)
                SentryErrorHandler.reportError(
                    exception = e,
                    component = "MainViewModel",
                    operation = "navigate_to_service_by_id"
                )
            }
        }
    }

    /**
     * Navigate to a specific service
     */
    fun navigateToService(service: Service) {
        Log.d(TAG, "Navigating to service: ${service.title}")
        _selectedServiceId.value = service.id
        _currentNotification.value = null
    }

    /**
     * Navigate to all services list
     */
    fun navigateToAllServices() {
        Log.d(TAG, "Navigating to all services")
        _selectedServiceId.value = null
        _currentNotification.value = null
    }

    /**
     * Navigate to a specific notification
     */
    fun navigateToNotification(notification: Notification) {
        Log.d(TAG, "Navigating to notification: ${notification.id}")
        _currentNotification.value = notification
    }

    /**
     * Navigate to a notification by ID (fetches from database)
     */
    fun navigateToNotificationById(notificationId: String) {
        if (!ensureInitialized()) return

        viewModelScope.launch {
            try {
                Log.d(TAG, "Navigating to notification by ID: $notificationId")

                if (!DatabaseManager.isInitialized()) {
                    Log.w(TAG, "DatabaseManager not initialized, cannot navigate to notification")
                    return@launch
                }

                val notificationRepository = DatabaseManager.getNotificationRepository()
                val notification = notificationRepository.getNotificationById(notificationId)

                if (notification != null) {
                    Log.d(TAG, "Found notification: ${notification.title}")

                    // Set the selected service so back navigation works correctly
                    // This ensures when user presses back, they return to the correct service screen
                    _selectedServiceId.value = notification.serviceId
                    _currentNotification.value = notification
                } else {
                    Log.w(TAG, "Notification not found with ID: $notificationId")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error navigating to notification by ID: $notificationId", e)
            }
        }
    }

    /**
     * Navigate back from notification to service
     */
    fun navigateBackFromNotification() {
        Log.d(TAG, "Navigating back from notification")
        _currentNotification.value = null
    }

    /**
     * Get or create ServiceViewModel for a given service ID
     */
    fun getServiceViewModel(serviceId: String): ServiceViewModel {
        return serviceViewModels.getOrPut(serviceId) {
            Log.d(TAG, "Creating new ServiceViewModel for serviceId: $serviceId")
            ServiceViewModel(serviceId, appContext)
        }
    }

    /**
     * Process startup URL using StartupUrlProcessor
     */
    fun processStartupUrl(url: String, done: () -> Unit) {
        viewModelScope.launch {
            try {
                val context = appContext
                val processor = StartupUrlProcessor.getInstance(context)
                val service = processor.processUrl(url)
                if (service != null) {
                    navigateToService(service)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error processing startup URL", e)
            }
            done()
        }
    }

    /**
     * Handle install referrer processing
     */
    fun handleInstallReferrer(context: Context, done: () -> Unit) {
        Log.d(TAG, "Handling install referrer")
        InstallReferrer.onValidUrl = { url ->
            Log.d(TAG, "Install referrer URL constructed: $url")
            processStartupUrl(url, this::intentDone)
        }
        InstallReferrer.onComplete = done
        InstallReferrer.detectInstallReferrer(context)
    }

    private fun refreshAllWidgets() {
        if (!ensureInitialized()) return

        Log.d(TAG, "Refreshing all widgets")

        viewModelScope.launch {
            try {
                AppioWidget().updateAll(appContext)
                Log.d(TAG, "All widgets updated")
            } catch (_: CancellationException) {
                // Ignore cancellation exceptions since we're using stateIn
                Log.e(TAG, "Services flow cancelled")
            } catch (e: Exception) {
                Log.e(TAG, "Error refreshing widgets", e)
                SentryErrorHandler.reportError(
                    exception = e,
                    component = "MainViewModel",
                    operation = "refresh_widgets"
                )
            }
        }
    }

    fun onStart() {
        Log.d(TAG, "onStart called")
        refreshAllWidgets()
    }

    fun onStop() {
        Log.d(TAG, "onStop")
        refreshAllWidgets()
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "MainViewModel cleared - cleaning up ServiceViewModels")
        // Clear all ServiceViewModels when MainViewModel is cleared
        serviceViewModels.clear()
    }
}