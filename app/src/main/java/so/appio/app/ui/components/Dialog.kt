package so.appio.app.ui.components

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.NotificationsActive
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
@Composable
fun Dialog(
    modifier: Modifier = Modifier,
    onDismissRequest: () -> Unit,
    title: String,
    message: String? = null,
    confirmButtonText: String,
    dismissButtonText: String? = null,
    onConfirm: () -> Unit,
    onDismiss: (() -> Unit)? = null
) {
    Dialog(
        onDismissRequest = onDismissRequest,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false
        )
    ) {
        DialogContent(
            title = title,
            message = message,
            confirmButtonText = confirmButtonText,
            dismissButtonText = dismissButtonText,
            onConfirm = onConfirm,
            onDismiss = onDismiss ?: onDismissRequest,
            modifier = modifier
        )
    }
}

@Composable
private fun DialogContent(
    title: String,
    message: String?,
    confirmButtonText: String,
    dismissButtonText: String?,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 0.dp, vertical = 16.dp), // No horizontal padding
        shape = RoundedCornerShape(32.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 24.dp
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp), // Smaller padding
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.NotificationsActive,
                    contentDescription = "Notification",
                    modifier = Modifier.size(32.dp),
                    tint = Color(0xFF495d92) // Darker blue color
                )
            }

            Spacer(modifier = Modifier.height(20.dp))

            val titleText = buildAnnotatedString {
                val parts = title.split(" ")
                var isAppName = false
                parts.forEachIndexed { index, part ->
                    if (part.contains("Appio") || part.contains("Test")) {
                        isAppName = true
                        withStyle(style = SpanStyle(fontWeight = FontWeight.Bold)) {
                            append(part)
                        }
                    } else {
                        withStyle(style = SpanStyle(fontWeight = FontWeight.Normal)) {
                            append(part)
                        }
                    }
                    if (index < parts.size - 1) append(" ")
                }
            }

            Text(
                text = titleText,
                style = MaterialTheme.typography.bodyLarge.copy(
                    fontWeight = FontWeight.Normal,
                    fontSize = 18.sp // Smaller font
                ),
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )

            // Message (if provided)
            message?.let {
                Spacer(modifier = Modifier.height(16.dp))
                Text(
                    text = it,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth()
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Buttons - stacked vertically with slight gap
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(4.dp) // More space between buttons
            ) {
                // Confirm button (first) - only top corners rounded
                DialogButton(
                    onClick = onConfirm,
                    text = confirmButtonText,
                    isTopButton = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // Dismiss button (second, if provided) - only bottom corners rounded
                dismissButtonText?.let {
                    DialogButton(
                        onClick = onDismiss,
                        text = it,
                        isTopButton = false,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

@Composable
fun DialogButton(
    onClick: () -> Unit,
    text: String,
    isTopButton: Boolean,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val backgroundColor = Color(0xFFdbe2ff) // Light blue background for both buttons
    val contentColor = Color.Black // Black text for both buttons

    val shape = if (isTopButton) {
        RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp, bottomStart = 4.dp, bottomEnd = 4.dp)
    } else {
        RoundedCornerShape(topStart = 4.dp, topEnd = 4.dp, bottomStart = 16.dp, bottomEnd = 16.dp)
    }

    Button(
        onClick = onClick,
        modifier = modifier.height(50.dp), // Slightly smaller height
        enabled = enabled,
        shape = shape,
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContainerColor = backgroundColor.copy(alpha = 0.38f),
            disabledContentColor = contentColor.copy(alpha = 0.38f)
        ),
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = 0.dp
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium.copy(
                fontWeight = FontWeight.Normal,
                fontSize = 14.sp // Smaller font
            )
        )
    }
}

// Keep the old button for other components that might use it
enum class ButtonStyle {
    Primary,
    Secondary
}

@Composable
fun Button(
    onClick: () -> Unit,
    text: String,
    style: ButtonStyle,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    val backgroundColor = when (style) {
        ButtonStyle.Primary -> MaterialTheme.colorScheme.primary
        ButtonStyle.Secondary -> MaterialTheme.colorScheme.surface
    }

    val contentColor = when (style) {
        ButtonStyle.Primary -> MaterialTheme.colorScheme.onPrimary
        ButtonStyle.Secondary -> MaterialTheme.colorScheme.primary
    }

    val borderColor = when (style) {
        ButtonStyle.Primary -> Color.Transparent
        ButtonStyle.Secondary -> MaterialTheme.colorScheme.outline
    }

    Button(
        onClick = onClick,
        modifier = modifier.height(48.dp),
        enabled = enabled,
        shape = RoundedCornerShape(24.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = backgroundColor,
            contentColor = contentColor,
            disabledContainerColor = backgroundColor.copy(alpha = 0.38f),
            disabledContentColor = contentColor.copy(alpha = 0.38f)
        ),
        border = if (style == ButtonStyle.Secondary) {
            androidx.compose.foundation.BorderStroke(1.dp, borderColor)
        } else null,
        elevation = ButtonDefaults.buttonElevation(
            defaultElevation = if (style == ButtonStyle.Primary) 2.dp else 0.dp
        )
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge.copy(
                fontWeight = FontWeight.Medium
            )
        )
    }
}
