package so.appio.app.ui.components

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.HideImage
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import so.appio.app.ui.theme.AppioAppTheme
import so.appio.app.utils.ImageCacheManager
import androidx.compose.material3.LoadingIndicator
import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun CachedImage(
    url: String?,
    modifier: Modifier = Modifier,
    contentDescription: String? = null,
    size: Dp = 120.dp,
    cornerRadius: Dp = 16.dp,
    contentScale: ContentScale = ContentScale.Crop,
    showLoadingIndicator: Boolean = true,
    loadingIndicatorColor: Color? = null,
) {
    var imageBitmap by remember(url) { mutableStateOf<Bitmap?>(null) }
    var isLoading by remember(url) { mutableStateOf(true) }

    // Load the image when URL changes
    LaunchedEffect(url) {
        url?.let { url ->
            isLoading = true
            imageBitmap = ImageCacheManager.getCachedImage(url = url)
            isLoading = false
        }
    }

    Box(
        modifier = modifier.size(size),
        contentAlignment = Alignment.Center
    ) {
        when {
            isLoading && showLoadingIndicator -> {
                LoadingIndicator(
                    modifier = Modifier.fillMaxSize(0.5f),
                    color = loadingIndicatorColor ?: LoadingIndicatorDefaults.indicatorColor
                )
            }
            imageBitmap != null -> {
                Image(
                    bitmap = imageBitmap!!.asImageBitmap(),
                    contentDescription = contentDescription,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(cornerRadius)),
                    contentScale = contentScale
                )
            }
            else -> {
                // Fallback empty state (shouldn't normally reach here)
                Box(
                    modifier = Modifier.fillMaxSize(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Outlined.HideImage,
                        contentDescription = "No image",
                        tint = MaterialTheme.colorScheme.outline,
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CachedImagePreview() {
    AppioAppTheme {
        CachedImage(
            url = "https://cdn.appio.so/app/demo.appio.so/logo.png",
            size = 120.dp,
            cornerRadius = 16.dp,
            showLoadingIndicator = true
        )
    }
}
