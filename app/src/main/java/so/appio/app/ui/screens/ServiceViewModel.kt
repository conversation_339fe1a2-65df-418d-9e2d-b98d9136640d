package so.appio.app.ui.screens

import android.content.Context
import android.util.Log
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.getAppWidgetState
import androidx.glance.state.PreferencesGlanceStateDefinition
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.utils.ServiceManager
import so.appio.app.utils.NotificationManager
import so.appio.app.utils.SentryErrorHandler
import so.appio.app.widgets.AppioWidget

/**
 * Data class to hold scroll position information
 */
data class ScrollPosition(
    val firstVisibleItemIndex: Int,
    val firstVisibleItemScrollOffset: Int
)

/**
 * ViewModel for ServiceScreen that handles service-specific state and operations.
 *
 * Provides database observables for notifications and handles refresh operations.
 * This ViewModel automatically observes database changes and updates the UI accordingly.
 */
class ServiceViewModel(
    private val serviceId: String,
    private val context: Context
) : ViewModel() {

    companion object {
        private const val TAG = "LOG:ServiceViewModel"
    }

    // Dependencies
    private val notificationRepository = DatabaseManager.getNotificationRepository()
    private val serviceManager = ServiceManager(context)
    private val notificationManager = NotificationManager(context)

    // Loading state for refresh operations
    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing

    // Track if we've received the first database result
    private val _hasReceivedFirstResult = MutableStateFlow(false)

    // Widget detection state - tracks if there are configured widgets for this service
    private val _hasConfiguredWidgets = MutableStateFlow(false)
    val hasConfiguredWidgets: StateFlow<Boolean> = _hasConfiguredWidgets

    // Scroll position state for preserving scroll position across navigation
    private var savedScrollPosition: ScrollPosition? = null

    // Database observable for notifications - automatically updates when database changes
    val notifications: StateFlow<List<Notification>> = notificationRepository
        .getNotificationsByService(serviceId)
        .onEach {
            // Mark that we've received the first result
            if (!_hasReceivedFirstResult.value) {
                _hasReceivedFirstResult.value = true
                Log.d(TAG, "First database result received for serviceId: $serviceId")
            }
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = emptyList()
        )

    // Expose whether we should show initial loading state
    val isInitialLoading: StateFlow<Boolean> = _hasReceivedFirstResult
        .map { hasReceived -> !hasReceived }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = true
        )

    init {
        Log.d(TAG, "ServiceViewModel initialized for serviceId: $serviceId")

        // Check for configured widgets on initialization
        checkForConfiguredWidgets()
    }

    /**
     * Refresh notifications for this service.
     * This will fetch fresh data from the API and update the database,
     * which will automatically trigger UI updates through the database observable.
     */
    fun refresh() {
        if (_isRefreshing.value) {
            Log.d(TAG, "Refresh already in progress, ignoring request")
            return
        }

        Log.d(TAG, "Starting refresh for serviceId: $serviceId")

        viewModelScope.launch {
            try {
                _isRefreshing.value = true

                // First, refresh service data
                val result = serviceManager.fetchAndStoreServiceWithWidgets(serviceId)
                if (result == null) {
                    Log.w(TAG, "Service refresh failed for serviceId: $serviceId")
                    return@launch
                }

                // Then, fetch and store notifications
                notificationManager.fetchAndStoreNotifications(serviceId)

                Log.d(TAG, "Refresh completed successfully for serviceId: $serviceId")

                // Check for configured widgets after refresh
                checkForConfiguredWidgets()

            } catch (e: Exception) {
                Log.e(TAG, "Error during refresh for serviceId: $serviceId", e)
                SentryErrorHandler.reportError(
                    exception = e,
                    component = "ServiceViewModel",
                    operation = "refresh"
                )
            } finally {
                _isRefreshing.value = false
            }
        }
    }

    /**
     * Save the current scroll position
     */
    fun saveScrollPosition(firstVisibleItemIndex: Int, firstVisibleItemScrollOffset: Int) {
        savedScrollPosition = ScrollPosition(firstVisibleItemIndex, firstVisibleItemScrollOffset)
        Log.d(TAG, "Saved scroll position: index=$firstVisibleItemIndex, offset=$firstVisibleItemScrollOffset")
    }

    /**
     * Get the saved scroll position, or null if none exists
     */
    fun getSavedScrollPosition(): ScrollPosition? {
        return savedScrollPosition
    }

    /**
     * Clear the saved scroll position
     */
    fun clearSavedScrollPosition() {
        savedScrollPosition = null
        Log.d(TAG, "Cleared saved scroll position")
    }

    /**
     * Refresh widget detection - public method to be called when app resumes
     */
    fun refreshWidgetDetection() {
        Log.d(TAG, "Refreshing widget detection for serviceId: $serviceId")
        checkForConfiguredWidgets()
    }

    /**
     * Check if there are any configured widgets for this service.
     * This checks for active Glance widgets on the homescreen that are configured for this service.
     */
    private fun checkForConfiguredWidgets() {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val glanceManager = GlanceAppWidgetManager(context)
                val glanceIds = glanceManager.getGlanceIds(AppioWidget::class.java)

                var hasConfiguredWidget = false

                // Check each widget to see if it's configured for this service
                for (glanceId in glanceIds) {
                    try {
                        val state = getAppWidgetState(context, PreferencesGlanceStateDefinition, glanceId)
                        val widgetServiceId = state[AppioWidget.KEY_SERVICE_ID]
                        val widgetId = state[AppioWidget.KEY_WIDGET_ID]
                        val widgetConfig = state[AppioWidget.KEY_WIDGET_CONFIG]

                        // Widget is considered configured if it has both serviceId and widgetId
                        if (widgetServiceId == serviceId && widgetId != null && widgetConfig != null) {
                            hasConfiguredWidget = true
                            Log.d(TAG, "Found configured widget for serviceId: $serviceId, widgetId: $widgetId")
                            break
                        }
                    } catch (e: Exception) {
                        Log.w(TAG, "Error reading widget state for glanceId: $glanceId", e)
                    }
                }

                _hasConfiguredWidgets.value = hasConfiguredWidget
                Log.d(TAG, "Widget detection completed for serviceId: $serviceId, hasConfiguredWidgets: $hasConfiguredWidget")

            } catch (e: Exception) {
                Log.e(TAG, "Error checking for configured widgets for serviceId: $serviceId", e)
                SentryErrorHandler.reportError(
                    exception = e,
                    component = "ServiceViewModel",
                    operation = "check_configured_widgets"
                )
                _hasConfiguredWidgets.value = false
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "ServiceViewModel cleared for serviceId: $serviceId")
    }
}
