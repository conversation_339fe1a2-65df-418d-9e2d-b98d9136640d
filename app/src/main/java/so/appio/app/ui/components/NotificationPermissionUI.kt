package so.appio.app.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import so.appio.app.MainActivity

/**
 * NotificationPermissionUI - Renders notification permission dialogs.
 *
 * This composable:
 * - Observes the NotificationPermissionManager from MainActivity
 * - Shows dialogs when needed
 * - Minimal footprint - just UI rendering
 *
 * Usage: Place this once in AppioAppTheme
 */
@Composable
fun NotificationPermissionUI() {
    val context = LocalContext.current

    // Get the manager from MainActivity (if available)
    val activity = context as? MainActivity ?: return
    val manager = activity.getNotificationPermissionManager()

    // Show dialog when needed
    if (manager.showCustomDialog) {
        NotificationPermissionDialog(
            onAllow = manager::onAllowCustomDialog,
            onDontAllow = manager::dismissCustomDialog,
            onDismiss = manager::dismissCustomDialog
        )
    }
}
