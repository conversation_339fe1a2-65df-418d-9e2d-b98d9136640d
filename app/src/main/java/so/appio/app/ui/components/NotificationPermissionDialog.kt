package so.appio.app.ui.components

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import so.appio.app.R

/**
 * Custom notification permission dialog.
 * Shows when the app needs to request notification permissions for SDK < 33
 * or when permissions are granted but notifications are disabled in settings.
 */
@Composable
fun NotificationPermissionDialog(
    onAllow: () -> Unit,
    onDontAllow: () -> Unit,
    onDismiss: () -> Unit = onDontAllow
) {
    val context = LocalContext.current
    val appName = context.getString(R.string.app_name)

    Dialog(
        onDismissRequest = onDismiss,
        title = "Allow $appName to send you notifications?",
        message = null,
        confirmButtonText = "Allow",
        dismissButtonText = "Don't Allow",
        onConfirm = onAllow,
        onDismiss = onDontAllow
    )
}
