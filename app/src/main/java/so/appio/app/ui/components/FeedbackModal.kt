package so.appio.app.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import android.util.Log
import androidx.compose.ui.text.style.TextAlign
import kotlinx.coroutines.launch
import so.appio.app.network.APIService
import so.appio.app.ui.screens.MainViewModel

private const val MAX_FEEDBACK_CHARACTERS = 5000

/**
 * Reusable feedback modal component that can be used across different screens.
 * Handles feedback submission internally and dismisses automatically.
 *
 * @param serviceId The service ID to associate with the feedback
 * @param onDismiss Callback when the modal is dismissed
 * @param modifier Optional modifier for the modal
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FeedbackModal(
    serviceId: String?,
    deviceId: String?,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var feedbackText by remember { mutableStateOf("") }
    var isSubmitted by remember { mutableStateOf(false) }
    var isSubmitting by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    val coroutineScope = rememberCoroutineScope()

    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        modifier = modifier,
        sheetState = sheetState,
        dragHandle = {
            BottomSheetDefaults.DragHandle()
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.8f) // Take up 80% of screen height
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 24.dp)
                .padding(bottom = 32.dp)
        ) {
            if (!isSubmitted) {
                // Header
                Text(
                    text = "Feedback",
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 24.dp)
                )

                // Multi-line text field
                OutlinedTextField(
                    value = feedbackText,
                    onValueChange = { newText ->
                        if (newText.length <= MAX_FEEDBACK_CHARACTERS) {
                            feedbackText = newText
                        }
                    },
                    placeholder = {
                        Text(
                            text = "Found a problem? Let us know.",
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    },
                    supportingText = {
                        val warningThreshold = (MAX_FEEDBACK_CHARACTERS * 0.9).toInt()
                        Text(
                            text = "${feedbackText.length}/$MAX_FEEDBACK_CHARACTERS",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (feedbackText.length > warningThreshold) {
                                MaterialTheme.colorScheme.error
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )

                        errorMessage?.let { error ->
                            Text(
                                text = error,
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.error,
                                textAlign = TextAlign.End,
                                modifier = Modifier.fillMaxWidth(),
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp), // Fixed height for consistent appearance
                    minLines = 8,
                    maxLines = 12,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = MaterialTheme.colorScheme.primary,
                        unfocusedBorderColor = MaterialTheme.colorScheme.outline,
                        focusedLabelColor = MaterialTheme.colorScheme.primary,
                        unfocusedLabelColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Send button
                Button(
                    onClick = {
                        coroutineScope.launch {
                            try {
                                isSubmitting = true
                                errorMessage = null
                                Log.d(MainViewModel.TAG_UI + ":FeedbackModal", "Submitting feedback for serviceId: $serviceId, serviceId: $deviceId")

                                val success = APIService.submitFeedback(serviceId ?: "", deviceId ?: "", feedbackText.trim())
                                if (success) {
                                    Log.d(MainViewModel.TAG_UI + ":FeedbackModal", "Feedback submitted successfully")
                                    isSubmitted = true
                                } else {
                                    Log.e(MainViewModel.TAG_UI + ":FeedbackModal", "Feedback submission failed")
                                    errorMessage = "Failed to send feedback. Please try again."
                                }
                            } catch (e: Exception) {
                                Log.e(MainViewModel.TAG_UI + ":FeedbackModal", "Error submitting feedback", e)
                                errorMessage = "Failed to send feedback. Please try again."
                            } finally {
                                isSubmitting = false
                            }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    ),
                    enabled = feedbackText.trim().isNotEmpty() && !isSubmitting
                ) {
                    if (isSubmitting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = MaterialTheme.colorScheme.onPrimary,
                            strokeWidth = 2.dp
                        )
                    } else {
                        Text(
                            text = "Send",
                            style = MaterialTheme.typography.labelLarge,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            } else {
                // Thank you message
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Thank you for your feedback",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.padding(bottom = 24.dp, top = 40.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Dismiss button
            TextButton(
                onClick = onDismiss,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = "Dismiss",
                    style = MaterialTheme.typography.labelLarge,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Powered by Appio
            PoweredByAppio(
                serviceId = "intro",
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}
