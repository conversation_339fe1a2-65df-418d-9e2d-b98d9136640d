package so.appio.app.ui.screens

import androidx.compose.foundation.border
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.sp
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.data.database.DatabaseManager
import so.appio.app.ui.components.CachedImage
import so.appio.app.utils.BrowserUtils
import so.appio.app.utils.DateUtils
import so.appio.app.utils.UrlValidator
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.graphics.Color
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import so.appio.app.ui.widgets.parseStringColor

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3ExpressiveApi::class)
@Composable
fun NotificationScreen(
    notification: Notification,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current

    // Get service from notification.serviceId
    var service by remember { mutableStateOf<Service?>(null) }

    LaunchedEffect(notification.serviceId) {
        service = withContext(Dispatchers.IO) {
            val serviceRepository = DatabaseManager.getServiceRepository()
            serviceRepository.getServiceById(notification.serviceId)
        }
    }

    val textColor = service?.let { parseStringColor(it.textColor) }
    val backgroundColor = service?.let { parseStringColor(it.backgroundColor) }

    Scaffold(
        modifier = modifier,
        containerColor = backgroundColor ?: MaterialTheme.colorScheme.surface,
        topBar = {
            MediumTopAppBar(
                colors = TopAppBarDefaults.topAppBarColors().let { defaultColors ->
                    TopAppBarDefaults.topAppBarColors(
                        containerColor = backgroundColor ?: defaultColors.containerColor,
                        titleContentColor = textColor ?: defaultColors.titleContentColor,
                        navigationIconContentColor = textColor ?: defaultColors.navigationIconContentColor
                    )
                },
                title = {
                    Text(
                        text = notification.title,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                }
            )
        },
        bottomBar = {
            if (UrlValidator.isValidBrowserUrl(notification.link)) {
                NotificationBottomBar(
                    onOpenLink = {
                        notification.link?.let { link ->
                            BrowserUtils.openUrl(context, link)
                        }
                    },
                    textColor = textColor,
                    backgroundColor = backgroundColor
                )
            }
        }
    ) { innerPadding ->
        NotificationContent(
            notification = notification,
            modifier = Modifier.padding(innerPadding),
            textColor = textColor,
            backgroundColor = backgroundColor
        )
    }
}

@Composable
private fun NotificationContent(
    notification: Notification,
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    backgroundColor: Color? = null
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
    ) {
        // Image section (if available)
        notification.imageUrl?.let { imageUrl ->
            CachedImage(
                url = imageUrl,
                contentDescription = "Notification image",
                cornerRadius = 0.dp,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(16f / 9f)
                    .padding(horizontal = 16.dp),
            )
        }

        // Content section
        Column(
            modifier = Modifier
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Date
            Text(
                text = DateUtils.formatDate(notification.receivedAt, false),
                style = MaterialTheme.typography.bodyMedium,
                color = textColor?.copy(alpha = 0.7f) ?: MaterialTheme.colorScheme.onSurfaceVariant
            )

            // Body content
            Text(
                text = notification.body,
                style = MaterialTheme.typography.bodyLarge,
                color = textColor ?: MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
private fun NotificationBottomBar(
    onOpenLink: () -> Unit,
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    backgroundColor: Color? = null
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .border(
                width = 1.dp,
                color = textColor?.copy(alpha = 0.1f) ?: MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
            )
            .navigationBarsPadding(),
        color = backgroundColor ?: MaterialTheme.colorScheme.surface,
//        shadowElevation = 8.dp
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
                .padding(
                    start = WindowInsets.safeDrawing.asPaddingValues().calculateStartPadding(LocalLayoutDirection.current),
                    end = WindowInsets.safeDrawing.asPaddingValues().calculateEndPadding(LocalLayoutDirection.current)
                ),
            contentAlignment = Alignment.Center
        ) {
            Button(
                onClick = onOpenLink,
                elevation = ButtonDefaults.buttonElevation(
                    defaultElevation = 5.dp
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(
                    // colors reversed on purpose
                    containerColor = textColor ?: MaterialTheme.colorScheme.primary,
                    contentColor = backgroundColor ?: MaterialTheme.colorScheme.onPrimary
                ),
            ) {
                Text(
                    text = "OPEN LINK",
                    style = MaterialTheme.typography.titleMedium.copy(
                        fontWeight = FontWeight.SemiBold,
                        fontSize = 16.sp
                    )
                )
            }
        }
    }
}
