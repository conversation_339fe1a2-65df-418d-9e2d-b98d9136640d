package so.appio.app.ui.widgets

import androidx.compose.runtime.Composable
import androidx.glance.layout.Box
import androidx.compose.ui.unit.dp
import androidx.glance.GlanceModifier
import androidx.glance.background
import androidx.glance.layout.padding
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.GlanceTheme.colors
import androidx.glance.layout.Alignment
import androidx.glance.layout.fillMaxSize
import androidx.glance.text.TextAlign

@Composable
fun WidgetConfigError() {
    Box(
        contentAlignment = Alignment.Center,
        modifier = GlanceModifier
            .fillMaxSize()
            .padding(16.dp)
            .background(colors.errorContainer),
    ) {
        Text(
            text = "Error decoding widget config.",
            style = TextStyle(
                color = colors.error,
                textAlign = TextAlign.Center,
            ),
        )
    }
}
