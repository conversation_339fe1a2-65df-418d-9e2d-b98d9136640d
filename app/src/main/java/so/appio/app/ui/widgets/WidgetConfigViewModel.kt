package so.appio.app.ui.widgets

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import so.appio.app.data.entity.service.Service
import so.appio.app.data.entity.widget.Widget
import so.appio.app.data.database.DatabaseManager

class WidgetConfigViewModel : ViewModel() {
    private val _services = MutableStateFlow<List<Service>>(emptyList())
    val services: StateFlow<List<Service>> = _services.asStateFlow()

    private val _widgets = MutableStateFlow<List<Widget>>(emptyList())
    val widgets: StateFlow<List<Widget>> = _widgets.asStateFlow()

    private val _isLoadingServices = MutableStateFlow(true)
    val isLoadingServices: StateFlow<Boolean> = _isLoadingServices.asStateFlow()

    init {
        loadServices()
    }

    private fun loadServices() {
        viewModelScope.launch {
            try {
                val serviceRepository = DatabaseManager.getServiceRepository()
                val servicesList = serviceRepository.getAllServicesList()
                _services.value = servicesList
            } catch (_: Exception) {
                // Handle error - for now just log and keep empty list
                _services.value = emptyList()
            } finally {
                _isLoadingServices.value = false
            }
        }
    }

    fun loadWidgetsForService(serviceId: String) {
        viewModelScope.launch {
            try {
                val widgetRepository = DatabaseManager.getWidgetRepository()
                val widgetsList = widgetRepository.getWidgetsByServiceList(serviceId)
                _widgets.value = widgetsList
            } catch (_: Exception) {
                // Handle error - for now just log and keep empty list
                _widgets.value = emptyList()
            }
        }
    }


}
