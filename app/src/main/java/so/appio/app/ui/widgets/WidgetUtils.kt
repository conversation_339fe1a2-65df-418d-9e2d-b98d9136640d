package so.appio.app.ui.widgets

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.glance.GlanceModifier
import androidx.glance.layout.padding
import androidx.glance.text.FontWeight
import androidx.glance.text.TextAlign
import androidx.glance.GlanceTheme.colors
import androidx.glance.unit.ColorProvider
import androidx.glance.text.TextDefaults
import kotlinx.serialization.json.Json
import androidx.core.graphics.toColorInt
import androidx.compose.foundation.isSystemInDarkTheme
import so.appio.app.data.entity.widget.*
import kotlinx.serialization.json.JsonElement
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.contentOrNull
import so.appio.app.widgets.AppioWidget

// Utility functions for property conversion
fun convertFontWeight(fontWeight: String?): FontWeight {
    return when (fontWeight?.lowercase()) {
        "bold" -> FontWeight.Bold
        "semibold" -> FontWeight.Medium
        "medium" -> FontWeight.Medium
        "normal" -> FontWeight.Normal
        else -> FontWeight.Normal
    }
}

fun convertTextAlign(textAlign: String?): TextAlign {
    return when (textAlign?.lowercase()) {
        "center" -> TextAlign.Center
        "start" -> TextAlign.Start
        "end" -> TextAlign.End
        "left" -> TextAlign.Left
        "right" -> TextAlign.Right
        else -> TextAlign.Start
    }
}

fun convertHorizontalAlignment(alignment: String?): androidx.glance.layout.Alignment.Horizontal {
    return when (alignment?.lowercase()) {
        "center" -> androidx.glance.layout.Alignment.CenterHorizontally
        "start" -> androidx.glance.layout.Alignment.Start
        "end" -> androidx.glance.layout.Alignment.End
        else -> androidx.glance.layout.Alignment.CenterHorizontally
    }
}

fun convertVerticalAlignment(alignment: String?): androidx.glance.layout.Alignment.Vertical {
    return when (alignment?.lowercase()) {
        "center" -> androidx.glance.layout.Alignment.CenterVertically
        "top" -> androidx.glance.layout.Alignment.Top
        "bottom" -> androidx.glance.layout.Alignment.Bottom
        else -> androidx.glance.layout.Alignment.CenterVertically
    }
}

fun convertBoxAlignment(horizontalAlignment: String?, verticalAlignment: String?): androidx.glance.layout.Alignment {
    val horizontal = horizontalAlignment?.lowercase()
    val vertical = verticalAlignment?.lowercase()

    return when {
        horizontal == "center" && vertical == "center" -> androidx.glance.layout.Alignment.Center
        horizontal == "start" && vertical == "top" -> androidx.glance.layout.Alignment.TopStart
        horizontal == "center" && vertical == "top" -> androidx.glance.layout.Alignment.TopCenter
        horizontal == "end" && vertical == "top" -> androidx.glance.layout.Alignment.TopEnd
        horizontal == "start" && vertical == "center" -> androidx.glance.layout.Alignment.CenterStart
        horizontal == "end" && vertical == "center" -> androidx.glance.layout.Alignment.CenterEnd
        horizontal == "start" && vertical == "bottom" -> androidx.glance.layout.Alignment.BottomStart
        horizontal == "center" && vertical == "bottom" -> androidx.glance.layout.Alignment.BottomCenter
        horizontal == "end" && vertical == "bottom" -> androidx.glance.layout.Alignment.BottomEnd
        else -> androidx.glance.layout.Alignment.Center
    }
}

/**
 * Converts a color string to a ColorProvider.
 * Hex: #00fF00 but not #0F0
 */
@SuppressLint("RestrictedApi") // ColorProvider still works
@Composable
fun convertColorProvider(colorString: String?): ColorProvider {
    if (colorString == null) return TextDefaults.defaultTextColor

    var colorVal = colorString.lowercase().trim()

    // Handle conditional colors (light,dark format)
    val multiColors = colorVal.split(",")
    if (multiColors.size == 2) {
        val lightColor = multiColors[0].trim()
        val darkColor = multiColors[1].trim()
        val isDarkTheme = isSystemInDarkTheme()
        colorVal = if (isDarkTheme) darkColor else lightColor
    }

    // If string is a valid color
    parseStringColor(colorVal)?.let { return ColorProvider(it) }

    // Colors from androidx.glance.color.ColorProviders
    return when (colorVal) {
        "primary" -> colors.primary
        "onprimary" -> colors.onPrimary
        "primarycontainer" -> colors.primaryContainer
        "onprimarycontainer" -> colors.onPrimaryContainer
        "secondary" -> colors.secondary
        "onsecondary" -> colors.onSecondary
        "secondarycontainer" -> colors.secondaryContainer
        "onsecondarycontainer" -> colors.onSecondaryContainer
        "tertiary" -> colors.tertiary
        "ontertiary" -> colors.onTertiary
        "tertiarycontainer" -> colors.tertiaryContainer
        "ontertiarycontainer" -> colors.onTertiaryContainer
        "error" -> colors.error
        "errorcontainer" -> colors.errorContainer
        "onerror" -> colors.onError
        "onerrorcontainer" -> colors.onErrorContainer
        "background" -> colors.background
        "onbackground" -> colors.onBackground
        "surface" -> colors.surface
        "onsurface" -> colors.onSurface
        "surfacevariant" -> colors.surfaceVariant
        "onsurfacevariant" -> colors.onSurfaceVariant
        "outline" -> colors.outline
        "inverseonsurface" -> colors.inverseOnSurface
        "inversesurface" -> colors.inverseSurface
        "inverseprimary" -> colors.inversePrimary
        "widgetbackground" -> colors.widgetBackground

        "clear" -> ColorProvider(Color.Transparent)

        // Matching iOS colors
        "brown" -> ColorProvider(parseStringColor("#A2845E")!!)
        "indigo" -> ColorProvider(parseStringColor("#5A5CFF")!!)
        "mint" -> ColorProvider(parseStringColor("#98FF98")!!)
        "orange" -> ColorProvider(parseStringColor("#FF9500")!!)
        "pink" -> ColorProvider(parseStringColor("#FF0000")!!)
        "purple" -> ColorProvider(parseStringColor("#AF52DE")!!)

        else -> TextDefaults.defaultTextColor
    }
}

/**
 * Converts a color string to an Int color value by leveraging the existing convertColorProvider logic.
 * This is used when an Int color is needed (e.g., for bitmap rendering).
 * Note: Theme colors (like "primary", "secondary") will fallback to default colors since
 * they can't be resolved to Int without a theme context.
 */
fun convertColor(colorString: String?): Int {
    if (colorString == null) return android.graphics.Color.BLACK

    val colorVal = colorString.lowercase().trim()

    // First try to parse as hex or named color
    parseStringColor(colorVal)?.let {
        val intColor = it.value.toInt()
        if (intColor != 0) {
            return intColor
        }
    }

    // Handle the same predefined colors as convertColorProvider function
    return when (colorVal) {
        "clear" -> android.graphics.Color.TRANSPARENT
        "brown" -> 0xFFA2845E.toInt()
        "indigo" -> 0xFF5A5CFF.toInt()
        "mint" -> 0xFF98FF98.toInt()
        "orange" -> 0xFFFF9500.toInt()
        "pink" -> 0xFFFF0000.toInt()
        "purple" -> 0xFFAF52DE.toInt()

        // For theme colors, we can't resolve them to Int without theme context,
        // so we provide reasonable defaults
        "primary" -> android.graphics.Color.BLUE
        "secondary" -> android.graphics.Color.GRAY
        "error" -> android.graphics.Color.RED
        "background" -> android.graphics.Color.WHITE
        "surface" -> android.graphics.Color.WHITE

        else -> android.graphics.Color.BLACK
    }
}



// Helper function to parse string color
// Hex color + `aqua`, `black`, `blue`, `cyan`, `darkgray`, `darkgrey`, `fuchsia`, `gray`, `green`, `grey`, `lightgray`, `lightgrey`, `lime`, `magenta`, `maroon`, `navy`, `olive`, `purple`, `red`, `silver`, `teal`, `white`, `yellow`
fun parseStringColor(colorString: String?): Color? {
    if (colorString == null) return null
    return try {
        Color(colorString.toColorInt())
    } catch (_: Exception) {
        null
    }
}

fun convertPadding(padding: PaddingProperties?): GlanceModifier {
    if (padding == null) return GlanceModifier

    val top = padding.top?.dp ?: 0.dp
    val bottom = padding.bottom?.dp ?: 0.dp
    val left = padding.left?.dp ?: 0.dp
    val right = padding.right?.dp ?: 0.dp

    return GlanceModifier.padding(
        start = left,
        top = top,
        end = right,
        bottom = bottom
    )
}

// Conditional modifier application - only apply if condition is true
fun GlanceModifier.conditional(
    condition: Boolean,
    modifier: GlanceModifier.() -> GlanceModifier
): GlanceModifier {
    return if (condition) {
        this.modifier()
    } else {
        this
    }
}

// Generic property parsing utility function
inline fun <reified T> parseProperties(jsonObject: kotlinx.serialization.json.JsonObject, elementType: String): T? {
    return try {
        val json = Json { ignoreUnknownKeys = true }
        json.decodeFromJsonElement(kotlinx.serialization.serializer<T>(), jsonObject)
    } catch (e: Exception) {
        Log.w(AppioWidget.TAG_UI + ":WidgetUtils", "Error parsing $elementType properties: ${e.message}")
        null
    }
}

// Specific parsing functions using the generic approach
fun parseTextProperties(jsonObject: kotlinx.serialization.json.JsonObject): TextProperties? {
    return parseProperties<TextProperties>(jsonObject, "text")
}

fun parseRowProperties(jsonObject: kotlinx.serialization.json.JsonObject): RowProperties? {
    return parseProperties<RowProperties>(jsonObject, "row")
}

fun parseColumnProperties(jsonObject: kotlinx.serialization.json.JsonObject): ColumnProperties? {
    return parseProperties<ColumnProperties>(jsonObject, "column")
}

fun parseBoxProperties(jsonObject: kotlinx.serialization.json.JsonObject): BoxProperties? {
    return parseProperties<BoxProperties>(jsonObject, "box")
}

fun parseImageProperties(jsonObject: kotlinx.serialization.json.JsonObject): ImageProperties? {
    return parseProperties<ImageProperties>(jsonObject, "image")
}

fun parseSpacerProperties(jsonObject: kotlinx.serialization.json.JsonObject): SpacerProperties? {
    return parseProperties<SpacerProperties>(jsonObject, "spacer")
}

fun parseGaugeProperties(jsonObject: kotlinx.serialization.json.JsonObject): GaugeProperties? {
    return parseProperties<GaugeProperties>(jsonObject, "gauge")
}

fun parseRefreshButtonProperties(jsonObject: kotlinx.serialization.json.JsonObject): RefreshButtonProperties? {
    return parseProperties<RefreshButtonProperties>(jsonObject, "refreshButton")
}

fun parseButtonProperties(jsonObject: kotlinx.serialization.json.JsonObject): ButtonProperties? {
    return parseProperties<ButtonProperties>(jsonObject, "button")
}

fun parseLastUpdatedProperties(jsonObject: kotlinx.serialization.json.JsonObject): LastUpdatedProperties? {
    return parseProperties<LastUpdatedProperties>(jsonObject, "lastUpdated")
}

// Helper functions to parse sizing values that can be either Int or "max" string
data class SizeValue(val isMax: Boolean, val value: Int?)

fun parseSizeValue(jsonElement: JsonElement?): SizeValue? {
    if (jsonElement == null) return null

    return when (jsonElement) {
        is JsonPrimitive -> {
            if (jsonElement.isString) {
                val content = jsonElement.contentOrNull
                if (content == "max") {
                    SizeValue(isMax = true, value = null)
                } else {
                    null // Invalid string value
                }
            } else {
                // Try to parse as int
                jsonElement.intOrNull?.let {
                    SizeValue(isMax = false, value = it)
                }
            }
        }
        else -> null
    }
}
