package so.appio.app.ui.components

import androidx.compose.ui.graphics.Color
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import so.appio.app.utils.BrowserUtils

/**
 * Reusable "Powered by Appio" component that opens the Appio website when clicked.
 * 
 * @param modifier Optional modifier for the text
 * @param serviceId Optional service ID to include in the URL for tracking (default: "app")
 */
@Composable
fun PoweredByAppio(
    modifier: Modifier = Modifier,
    textColor: Color? = null,
    serviceId: String = ""
) {
    val context = LocalContext.current
    
    Text(
        text = "Powered by Appio",
        style = MaterialTheme.typography.bodySmall,
        color = textColor ?: MaterialTheme.colorScheme.outline,
        modifier = modifier
            .clickable {
                BrowserUtils.openUrl(
                    context,
                    "https://appio.so?source=android&service=$serviceId"
                )
            }
            .padding(horizontal = 24.dp, vertical = 4.dp),
        textAlign = TextAlign.Center
    )
}
