package so.appio.app.ui.widgets

import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.Button
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.ExperimentalMaterial3ExpressiveApi
import androidx.compose.material3.LoadingIndicator
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import so.appio.app.data.entity.service.Service
import so.appio.app.data.entity.widget.Widget
import so.appio.app.ui.components.CachedImage

import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
data class WidgetConfigData(
    val serviceId: String,
    val widgetId: String,
    val widgetConfig: String
)

sealed class WidgetConfigResult {
    data class Success(val configData: WidgetConfigData) : WidgetConfigResult()
    object Cancelled : WidgetConfigResult()
}

@Composable
fun WidgetConfigScreen(
    onConfigurationResult: (WidgetConfigResult) -> Unit,
    initialServiceId: String? = null,
    initialWidgetId: String? = null
) {
    val viewModel: WidgetConfigViewModel = viewModel()
    val services by viewModel.services.collectAsState()
    val widgets by viewModel.widgets.collectAsState()
    val isLoadingServices by viewModel.isLoadingServices.collectAsState()

    var selectedService by remember { mutableStateOf<Service?>(null) }
    var selectedWidget by remember { mutableStateOf<Widget?>(null) }

    // Pre-select service and widget if initial values are provided
    LaunchedEffect(services, initialServiceId) {
        if (initialServiceId != null && selectedService == null) {
            val preSelectedService = services.find { it.id == initialServiceId }
            if (preSelectedService != null) {
                selectedService = preSelectedService
            }
        }
    }

    LaunchedEffect(widgets, initialWidgetId) {
        if (initialWidgetId != null && selectedWidget == null) {
            val preSelectedWidget = widgets.find { it.id == initialWidgetId }
            if (preSelectedWidget != null) {
                selectedWidget = preSelectedWidget
            }
        }
    }

    // Auto-select service if only one exists (but don't override pre-selection)
    LaunchedEffect(services, initialServiceId) {
        if (services.size == 1 && selectedService == null && initialServiceId == null) {
            selectedService = services.first()
        }
    }

    // Auto-select widget if only one exists (but don't override pre-selection)
    LaunchedEffect(widgets, initialWidgetId) {
        if (widgets.size == 1 && selectedWidget == null && initialWidgetId == null) {
            selectedWidget = widgets.first()
        }
    }

    // Load widgets when service is selected
    LaunchedEffect(selectedService) {
        selectedService?.let { service ->
            viewModel.loadWidgetsForService(service.id)
            // Only reset widget selection if this is not the initial preselection
            // If we have an initialWidgetId, we want to preserve the widget selection
            if (initialWidgetId == null) {
                selectedWidget = null // Reset widget selection only for manual service changes
            }
        }
    }

    val scrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background)
            .windowInsetsPadding(WindowInsets.safeDrawing)
            .verticalScroll(scrollState)
            .padding(horizontal = 32.dp, vertical = 64.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        when {
            isLoadingServices -> {
                // Add spacer to center loading content vertically
                Spacer(modifier = Modifier.weight(1f))
                LoadingContent()
                Spacer(modifier = Modifier.weight(1f))
            }
            services.isEmpty() -> {
                // Add spacer to center no services content vertically
                Spacer(modifier = Modifier.weight(1f))
                NoServicesContent(onConfigurationResult = onConfigurationResult)
                Spacer(modifier = Modifier.weight(1f))
            }
            services.size == 1 -> {
                SingleServiceContent(
                    service = selectedService,
                    widgets = widgets,
                    selectedWidget = selectedWidget,
                    onWidgetSelected = { selectedWidget = it },
                    onConfigurationResult = onConfigurationResult
                )
            }
            else -> {
                MultipleServicesContent(
                    services = services,
                    widgets = widgets,
                    selectedService = selectedService,
                    selectedWidget = selectedWidget,
                    onServiceSelected = { selectedService = it },
                    onWidgetSelected = { selectedWidget = it },
                    onConfigurationResult = onConfigurationResult
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3ExpressiveApi::class)
@Composable
private fun LoadingContent() {
    LoadingIndicator(
        modifier = Modifier.size(48.dp),
        color = MaterialTheme.colorScheme.primary
    )
}

@Composable
private fun NoServicesContent(
    onConfigurationResult: (WidgetConfigResult) -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "To get started, connect to\na service that uses Appio.",
            style = MaterialTheme.typography.displaySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 64.dp)
        )

        Button(
            onClick = {
                onConfigurationResult(WidgetConfigResult.Cancelled)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
        ) {
            Text(
                text = "Close",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
        }
    }
}

@Composable
private fun SingleServiceContent(
    service: Service?,
    widgets: List<Widget>,
    selectedWidget: Widget?,
    onWidgetSelected: (Widget?) -> Unit,
    onConfigurationResult: (WidgetConfigResult) -> Unit
) {
    ConfigurationHeader()

    if (service != null) {
        ServiceDisplay(service = service)
        WidgetSelection(
            widgets = widgets,
            selectedWidget = selectedWidget,
            onWidgetSelected = onWidgetSelected,
            isEnabled = true
        )
        ConfirmButton(
            isEnabled = selectedWidget != null,
            onConfirm = {
                service.let { svc ->
                    selectedWidget?.let { widget ->
                        onConfigurationResult(
                            WidgetConfigResult.Success(
                                WidgetConfigData(
                                    serviceId = svc.id,
                                    widgetId = widget.id,
                                    widgetConfig = widget.config
                                )
                            )
                        )
                    }
                }
            }
        )
    }
}

@Composable
private fun MultipleServicesContent(
    services: List<Service>,
    widgets: List<Widget>,
    selectedService: Service?,
    selectedWidget: Widget?,
    onServiceSelected: (Service?) -> Unit,
    onWidgetSelected: (Widget?) -> Unit,
    onConfigurationResult: (WidgetConfigResult) -> Unit
) {
    ConfigurationHeader()

    ServiceSelection(
        services = services,
        selectedService = selectedService,
        onServiceSelected = onServiceSelected
    )

    WidgetSelection(
        widgets = widgets,
        selectedWidget = selectedWidget,
        onWidgetSelected = onWidgetSelected,
        isEnabled = selectedService != null
    )

    ConfirmButton(
        isEnabled = selectedService != null && selectedWidget != null,
        onConfirm = {
            selectedService?.let { service ->
                selectedWidget?.let { widget ->
                    onConfigurationResult(
                        WidgetConfigResult.Success(
                            WidgetConfigData(
                                serviceId = service.id,
                                widgetId = widget.id,
                                widgetConfig = widget.config
                            )
                        )
                    )
                }
            }
        }
    )
}

@Composable
private fun ConfigurationHeader() {
    Text(
        text = "Configure Widget",
        style = MaterialTheme.typography.headlineMedium,
        fontWeight = FontWeight.Bold,
        color = MaterialTheme.colorScheme.onSurface
    )
}

@Composable
private fun ServiceDisplay(service: Service) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Service",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    MaterialTheme.colorScheme.surfaceVariant,
                    RoundedCornerShape(8.dp)
                )
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            CachedImage(
                url = service.logoURL,
                contentDescription = "${service.title} logo",
                size = 32.dp,
                cornerRadius = 6.dp,
                showLoadingIndicator = false
            )
            Text(
                text = service.title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

@Composable
private fun ServiceSelection(
    services: List<Service>,
    selectedService: Service?,
    onServiceSelected: (Service?) -> Unit
) {
    var showServiceDropdown by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Select Service",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        Box {
            OutlinedButton(
                onClick = { showServiceDropdown = true },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    if (selectedService != null) {
                        CachedImage(
                            url = selectedService.logoURL,
                            contentDescription = "${selectedService.title} logo",
                            size = 24.dp,
                            cornerRadius = 4.dp,
                            showLoadingIndicator = false
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                    }
                    Text(
                        text = selectedService?.title ?: "Select a service"
                    )
                }
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = "Dropdown"
                )
            }

            DropdownMenu(
                expanded = showServiceDropdown,
                onDismissRequest = { showServiceDropdown = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                services.forEach { service ->
                    DropdownMenuItem(
                        text = {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                CachedImage(
                                    url = service.logoURL,
                                    contentDescription = "${service.title} logo",
                                    size = 24.dp,
                                    cornerRadius = 4.dp,
                                    showLoadingIndicator = false
                                )
                                Text(service.title)
                            }
                        },
                        onClick = {
                            onServiceSelected(service)
                            showServiceDropdown = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun WidgetSelection(
    widgets: List<Widget>,
    selectedWidget: Widget?,
    onWidgetSelected: (Widget?) -> Unit,
    isEnabled: Boolean
) {
    var showWidgetDropdown by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = "Select Widget Template",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        Box {
            OutlinedButton(
                onClick = {
                    if (isEnabled) {
                        showWidgetDropdown = true
                    }
                },
                enabled = isEnabled,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text(
                    text = when {
                        !isEnabled -> "Select a service first"
                        selectedWidget != null -> selectedWidget.name
                        widgets.isEmpty() -> "No widget templates available"
                        else -> "Select a widget template"
                    },
                    modifier = Modifier.weight(1f)
                )
                Icon(
                    imageVector = Icons.Default.ArrowDropDown,
                    contentDescription = "Dropdown"
                )
            }

            DropdownMenu(
                expanded = showWidgetDropdown,
                onDismissRequest = { showWidgetDropdown = false },
                modifier = Modifier.fillMaxWidth()
            ) {
                widgets.forEach { widget ->
                    DropdownMenuItem(
                        text = { Text(widget.name) },
                        onClick = {
                            onWidgetSelected(widget)
                            showWidgetDropdown = false
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun ConfirmButton(
    isEnabled: Boolean,
    onConfirm: () -> Unit
) {
    Button(
        onClick = onConfirm,
        enabled = isEnabled,
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp)
    ) {
        Text(
            text = "Confirm",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold
        )
    }
}



