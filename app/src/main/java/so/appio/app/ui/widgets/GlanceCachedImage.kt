package so.appio.app.ui.widgets

import android.util.Log
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.unit.Dp
import androidx.glance.GlanceModifier
import androidx.glance.Image
import androidx.glance.ImageProvider
import androidx.glance.appwidget.cornerRadius
import androidx.glance.layout.Box
import androidx.glance.layout.ContentScale
import androidx.glance.layout.fillMaxSize
import kotlinx.coroutines.runBlocking
import so.appio.app.R
import so.appio.app.utils.ImageCacheManager
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import so.appio.app.widgets.AppioWidget

/**
 * Glance-compatible cached image component
 * Downloads and caches images for use in app widgets
 */
@Composable
fun GlanceCachedImage(
    filename: String?,
    modifier: GlanceModifier = GlanceModifier,
    cornerRadius: Dp? = null,
    contentScale: ContentScale = ContentScale.Fit,
) {
    if (filename.isNullOrBlank()) {
        return
    }

    var imageProvider by remember(filename) { mutableStateOf<ImageProvider?>(null) }
    val cachedImageProvider = getCachedImageProvider(filename)
    if (cachedImageProvider != null) {
        imageProvider = cachedImageProvider
    }

    Box(
        modifier = modifier
    ) {
        when {
            imageProvider != null -> {
                Image(
                    provider = imageProvider!!,
                    contentDescription = null,
                    modifier = //GlanceModifier
//                        .fillMaxSize()
                        modifier
                        .conditional(cornerRadius != null) {
                            cornerRadius(cornerRadius!!)
                        },
                    contentScale = contentScale
                )
            }
            else -> {
                // Fallback icon using ImageProvider
                Image(
                    provider = ImageProvider(R.drawable.ic_hide_image,),
                    contentDescription = "No image",
                    modifier = //GlanceModifier
//                        .fillMaxSize()
                        modifier
                        .conditional(cornerRadius != null) {
                            cornerRadius(cornerRadius!!)
                        },
                    contentScale = contentScale
                )
            }
        }
    }
}

/**
 * Get cached image as ImageProvider for Glance widgets
 * This function downloads and caches the image, then creates a bitmap-based ImageProvider
 */
private fun getCachedImageProvider(filename: String): ImageProvider? {
    val bitmap = runBlocking {
        // NOTE: we are not downloading image here, but relying on cache. if we have to, we can use
        // ImageCacheManager.getCachedImage(url) but this is not recommended during widget rendering
        // and we would need access to $url
        ImageCacheManager.getPreCachedImage(filename)
    }

    if (bitmap != null) {
        return ImageProvider(bitmap)
    } else {
        Log.w(AppioWidget.TAG_UI + ":GlanceCachedImage", "Failed to get cached bitmap from file: $filename")
        return null
    }
}


