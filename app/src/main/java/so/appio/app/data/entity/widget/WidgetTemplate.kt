package so.appio.app.data.entity.widget

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonElement

/**
 * Data classes for widget template configuration
 */
@Serializable
data class WidgetTemplate(
    @SerialName("variants")
    val variants: List<WidgetVariant>
) {
    /**
     * Pick a variant based on widget size
     * Orders variants by size (biggest first) - size = width * height
     */
    fun pickVariant(widthDp: androidx.compose.ui.unit.Dp, heightDp: androidx.compose.ui.unit.Dp): WidgetVariant? {
        // Sort variants by size (width * height) in descending order (biggest first)
        val sortedVariants = variants.sortedByDescending { variant ->
            val props = variant.properties
            val width = props.width ?: 0
            val height = props.height ?: 0
            width * height // Total area as size metric
        }

        return sortedVariants.firstOrNull { variant ->
            val props = variant.properties
            val widthMatches = props.width?.let { widthDp.value >= it } ?: true
            val heightMatches = props.height?.let { heightDp.value >= it } ?: true
            widthMatches && heightMatches
        } ?: variants.firstOrNull() // Fallback to first variant if no match
    }
}

@Serializable
data class WidgetVariant(
    @SerialName("properties")
    val properties: WidgetVariantProperties,

    @SerialName("elements")
    val elements: List<WidgetElement>
)

@Serializable
data class WidgetVariantProperties(
    @SerialName("version")
    val version: String? = null,

    @SerialName("width")
    val width: Int? = null,

    @SerialName("height")
    val height: Int? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("url")
    val url: String? = null,
)

@Serializable
data class WidgetElement(
    @SerialName("type")
    val type: String,

    // Defined as var because internal processing may update values
    @SerialName("properties")
    var properties: JsonObject = JsonObject(emptyMap()),

    @SerialName("elements")
    val elements: List<WidgetElement> = emptyList()
)

// Widget element types enum - Android-supported elements
enum class WidgetElementType(val value: String) {
    BOX("box"),
    COLUMN("column"),
    ROW("row"),
    IMAGE("image"),
    TEXT("text"),
    SPACER("spacer"),
    REFRESH_BUTTON("refreshButton"),
    LAST_UPDATED("lastUpdated");

    companion object {
        fun fromString(value: String): WidgetElementType? {
            return entries.find { it.value == value }
        }
    }
}

@Serializable
data class RowProperties(
    @SerialName("horizontalAlignment")
    val horizontalAlignment: String? = null,

    @SerialName("verticalAlignment")
    val verticalAlignment: String? = null,

    @SerialName("spacing")
    val spacing: Int? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("cornerRadius")
    val cornerRadius: Int? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null
)

@Serializable
data class ColumnProperties(
    @SerialName("horizontalAlignment")
    val horizontalAlignment: String? = null,

    @SerialName("verticalAlignment")
    val verticalAlignment: String? = null,

    @SerialName("spacing")
    val spacing: Int? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("cornerRadius")
    val cornerRadius: Int? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null
)

@Serializable
data class BoxProperties(
    @SerialName("horizontalAlignment")
    val horizontalAlignment: String? = null,

    @SerialName("verticalAlignment")
    val verticalAlignment: String? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("cornerRadius")
    val cornerRadius: Int? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null
)

@Serializable
data class TextProperties(
    @SerialName("text")
    val text: String? = null,

    @SerialName("fontSize")
    val fontSize: Int? = null,

    @SerialName("fontWeight")
    val fontWeight: String? = null,

    @SerialName("alignment")
    val alignment: String? = null,

    @SerialName("color")
    val color: String? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null
)

@Serializable
data class ImageProperties(
    // Internal parameter generated in pre-processing in WidgetImageManager
    @SerialName("__src")
    var __src: String? = null,

    @SerialName("src")
    val src: String? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null,

    @SerialName("cornerRadius")
    val cornerRadius: Int? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null
)

@Serializable
data class SpacerProperties(
    // int|`max`
    @SerialName("length")
    val length: JsonElement? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null
)

@Serializable
data class PaddingProperties(
    @SerialName("top")
    val top: Int? = null,

    @SerialName("bottom")
    val bottom: Int? = null,

    @SerialName("left")
    val left: Int? = null,

    @SerialName("right")
    val right: Int? = null,
)

@Serializable
data class GaugeProperties(
    // Internal parameter generated in pre-processing in WidgetImageManager
    @SerialName("__src")
    var __src: String? = null,

    @SerialName("label")
    val label: String? = null,

    // 0.0 to 100.0 the value will be divided by 100 to get the progress
    @SerialName("value")
    val value: Float? = null,

    @SerialName("color")
    val color: String? = null,

    // NOTE: Used in pre-caching images. Not standard color pallet. Anything that works with: String.toColorInt()
    @SerialName("tint")
    val tint: String? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    @SerialName("fontSize")
    val fontSize: Int? = null,

    @SerialName("fontWeight")
    val fontWeight: String? = null,

    // int|`max`
    @SerialName("size")
    val size: JsonElement? = null
)

@Serializable
data class RefreshButtonProperties(
    @SerialName("color")
    val color: String? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    @SerialName("text")
    val text: String? = null,

    @SerialName("tint")
    val tint: String? = null,

    // filled, outline, icon-primary, icon-secondary
    @SerialName("style")
    val style: String? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null
)

@Serializable
data class ButtonProperties(
    @SerialName("color")
    val color: String? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    @SerialName("text")
    val text: String? = null,

    @SerialName("tint")
    val tint: String? = null,

    // filled, outline, icon-primary, icon-secondary
    @SerialName("style")
    val style: String? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null,

    // URL for API call
    @SerialName("url")
    val url: String? = null
)

// Extending TextProperties
@Serializable
data class LastUpdatedProperties(
    @SerialName("fontSize")
    val fontSize: Int? = null,

    @SerialName("fontWeight")
    val fontWeight: String? = null,

    @SerialName("alignment")
    val alignment: String? = null,

    @SerialName("color")
    val color: String? = null,

    @SerialName("background")
    val background: String? = null,

    @SerialName("padding")
    val padding: PaddingProperties? = null,

    // int|`max`
    @SerialName("width")
    val width: JsonElement? = null,

    // int|`max`
    @SerialName("height")
    val height: JsonElement? = null,
)

