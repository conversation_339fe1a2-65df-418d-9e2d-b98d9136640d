package so.appio.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import so.appio.app.data.entity.notification.Notification

/**
 * Data Access Object (DAO) for Notification entity.
 * Provides methods to interact with the notifications table in the database.
 */
@Dao
interface NotificationDao {

    /**
     * Get notifications for a specific service as a Flow
     */
    @Query("SELECT * FROM notifications WHERE service_id = :serviceId ORDER BY received_at DESC")
    fun getNotificationsByService(serviceId: String): Flow<List<Notification>>
    
    /**
     * Get notifications for a specific service as a list
     */
    @Query("SELECT * FROM notifications WHERE service_id = :serviceId ORDER BY received_at DESC")
    suspend fun getNotificationsByServiceList(serviceId: String): List<Notification>
    
    /**
     * Get a notification by its ID
     */
    @Query("SELECT * FROM notifications WHERE id = :notificationId LIMIT 1")
    suspend fun getNotificationById(notificationId: String): Notification?
    
    /**
     * Get a notification by its ID as Flow for reactive updates
     */
    @Query("SELECT * FROM notifications WHERE id = :notificationId LIMIT 1")
    fun getNotificationByIdFlow(notificationId: String): Flow<Notification?>
    
    /**
     * Insert a new notification. Ignore if conflict occurs (preserves existing data).
     */
    @Insert(onConflict = OnConflictStrategy.IGNORE)
    suspend fun insertNotification(notification: Notification)
    
    /**
     * Update an existing notification
     */
    @Update
    suspend fun updateNotification(notification: Notification)


    
    /**
     * Get count of notifications for a specific service
     */
    @Query("SELECT COUNT(*) FROM notifications WHERE service_id = :serviceId")
    suspend fun getNotificationCountByService(serviceId: String): Int

    /**
     * Check if a notification exists by ID
     */
    @Query("SELECT EXISTS(SELECT 1 FROM notifications WHERE id = :notificationId)")
    suspend fun notificationExists(notificationId: String): Boolean
}
