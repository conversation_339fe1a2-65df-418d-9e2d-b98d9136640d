package so.appio.app.data.entity.featureflags

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Models for feature flag-related API operations
 */

@Serializable
data class FeatureFlagsEntity(
    @SerialName("id")
    val id: String = "",

    @SerialName("platform")
    val platform: String = "",

    @SerialName("version")
    val version: String = "",

    @SerialName("config")
    val config: String = ""
)
