package so.appio.app.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val Context.appDataStore: DataStore<Preferences> by preferencesDataStore(name = "app_preferences")

/**
 * AppDataStore manages app-wide preferences using Jetpack DataStore.
 *
 * This class consolidates all app preferences including notification settings,
 * user preferences, and other app-wide configuration data.
 */
class AppDataStore(private val context: Context) {

    companion object {
        private val KEY_NOTIFICATION_PERMISSION_REQUESTED = booleanPreferencesKey("app_notification_permission_requested")
    }

    // Notification preferences
    val isNotificationPermissionRequested: Flow<Boolean> = context.appDataStore.data
        .map { preferences ->
            preferences[KEY_NOTIFICATION_PERMISSION_REQUESTED] ?: false
        }

    suspend fun markNotificationPermissionAsRequested() {
        context.appDataStore.edit { preferences ->
            preferences[KEY_NOTIFICATION_PERMISSION_REQUESTED] = true
        }
    }

    suspend fun resetNotificationPermissionRequested() {
        context.appDataStore.edit { preferences ->
            preferences[KEY_NOTIFICATION_PERMISSION_REQUESTED] = false
        }
    }
}