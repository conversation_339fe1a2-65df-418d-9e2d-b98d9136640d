package so.appio.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import android.content.Context
import so.appio.app.data.converters.DateConverters
import so.appio.app.data.dao.NotificationDao
import so.appio.app.data.dao.ServiceDao
import so.appio.app.data.dao.WidgetDao
import so.appio.app.data.entity.notification.Notification
import so.appio.app.data.entity.service.Service
import so.appio.app.data.entity.widget.Widget
import so.appio.app.data.database.migrations.MIGRATION_1_2

/**
 * Room database for the Appio application.
 *
 * Contains all entities and provides access to DAOs.
 */
@Database(
    entities = [Service::class, Notification::class, Widget::class],
    version = 2,
    exportSchema = false
)
@TypeConverters(DateConverters::class)
abstract class AppDatabase : RoomDatabase() {
    
    /**
     * Provides access to Service DAO
     */
    abstract fun serviceDao(): ServiceDao

    /**
     * Provides access to Notification DAO
     */
    abstract fun notificationDao(): NotificationDao

    /**
     * Provides access to Widget DAO
     */
    abstract fun widgetDao(): WidgetDao
    
    companion object {
        private const val DATABASE_NAME = "appio_database"
        
        @Volatile
        private var INSTANCE: AppDatabase? = null
        
        /**
         * Get database instance using singleton pattern
         */
        fun getDatabase(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigration(true)
                .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * Clear the database instance (useful for developing)
         */
        fun clearInstance() {
            INSTANCE = null
        }

        /**
         * Delete the database file completely (useful for development reset)
         */
        fun deleteDatabase(context: Context) {
            clearInstance()
            context.applicationContext.deleteDatabase(DATABASE_NAME)
        }
    }
}
