package so.appio.app.data.repository

import kotlinx.coroutines.flow.Flow
import so.appio.app.data.dao.ServiceDao
import so.appio.app.data.entity.service.Service
import so.appio.app.data.database.DatabaseManager

/**
 * Repository for Service entity.
 * 
 * Provides a clean API for accessing Service data and abstracts the data source.
 * This repository currently uses Room database but could be extended to include
 * network calls, caching strategies, etc.
 */
class ServiceRepository(private val serviceDao: ServiceDao) {
    
    /**
     * Get all services as a Flow for reactive updates
     */
    fun getAllServices(): Flow<List<Service>> = serviceDao.getAllServices()
    
    /**
     * Get all services as a list (one-time query)
     */
    suspend fun getAllServicesList(): List<Service> = serviceDao.getAllServicesList()
    
    /**
     * Get a service by its ID
     */
    suspend fun getServiceById(serviceId: String): Service? = serviceDao.getServiceById(serviceId)
    
    /**
     * Get a service by its ID as Flow for reactive updates
     */
    fun getServiceByIdFlow(serviceId: String): Flow<Service?> = serviceDao.getServiceByIdFlow(serviceId)
    
    /**
     * Insert a new service
     */
    suspend fun insertService(service: Service) = serviceDao.insertService(service)
    
    /**
     * Update an existing service
     */
    suspend fun updateService(service: Service) = serviceDao.updateService(service)
    
    /**
     * Delete a service
     */
    suspend fun deleteService(service: Service) = serviceDao.deleteService(service)
    
    /**
     * Delete a service by its ID
     */
    suspend fun deleteServiceById(serviceId: String) = serviceDao.deleteServiceById(serviceId)
    
    /**
     * Update specific service fields (title, description, logoURL, bannerURL, url, textColor, backgroundColor)
     */
    suspend fun updateServiceFields(serviceId: String, title: String, description: String?, logoURL: String, bannerURL: String?, url: String?, textColor: String?, backgroundColor: String?) =
        serviceDao.updateServiceFields(serviceId, title, description, logoURL, bannerURL, url, textColor, backgroundColor)

    /**
     * Update notification status for a service
     */
    suspend fun updateNotificationStatus(serviceId: String, enabled: Boolean) =
        serviceDao.updateNotificationStatus(serviceId, enabled)
    
    /**
     * Get count of all services
     */
    suspend fun getServiceCount(): Int = serviceDao.getServiceCount()
    
    /**
     * Check if a service exists by ID
     */
    suspend fun serviceExists(serviceId: String): Boolean = serviceDao.serviceExists(serviceId)

    /**
     * Delete services that are NOT in the provided list of service IDs.
     * Also deletes all associated widgets for those services.
     *
     * @param serviceIds List of service IDs to keep (services NOT in this list will be deleted)
     */
    suspend fun deleteServicesNotInList(serviceIds: List<String>) {
        // First delete widgets for services that will be deleted
        val widgetRepository = DatabaseManager.getWidgetRepository()
        widgetRepository.deleteWidgetsForServicesNotInList(serviceIds)

        // Then delete the services themselves
        serviceDao.deleteServicesNotInList(serviceIds)
    }
}
