package so.appio.app.data.converters

import androidx.room.TypeConverter
import java.util.Date

/**
 * Type converters for Room database to handle Date objects.
 * Room doesn't know how to store Date objects, so we convert them to/from Long (timestamp).
 */
class DateConverters {
    
    /**
     * Convert Date to Long (timestamp) for storage in database
     */
    @TypeConverter
    fun fromDate(date: Date?): Long? {
        return date?.time
    }
    
    /**
     * Convert Long (timestamp) to Date for use in application
     */
    @TypeConverter
    fun toDate(timestamp: Long?): Date? {
        return timestamp?.let { Date(it) }
    }
}
