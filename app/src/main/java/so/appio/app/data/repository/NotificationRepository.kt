package so.appio.app.data.repository

import kotlinx.coroutines.flow.Flow
import so.appio.app.data.dao.NotificationDao
import so.appio.app.data.entity.notification.Notification

/**
 * Repository for Notification entity.
 * 
 * Provides a clean API for accessing Notification data and abstracts the data source.
 * This repository currently uses Room database but could be extended to include
 * network calls, caching strategies, etc.
 */
class NotificationRepository(private val notificationDao: NotificationDao) {
    
    /**
     * Get notifications for a specific service as a Flow
     */
    fun getNotificationsByService(serviceId: String): Flow<List<Notification>> = 
        notificationDao.getNotificationsByService(serviceId)
    
    /**
     * Get notifications for a specific service as a list
     */
    suspend fun getNotificationsByServiceList(serviceId: String): List<Notification> = 
        notificationDao.getNotificationsByServiceList(serviceId)
    
    /**
     * Get a notification by its ID
     */
    suspend fun getNotificationById(notificationId: String): Notification? = 
        notificationDao.getNotificationById(notificationId)
    
    /**
     * Get a notification by its ID as Flow for reactive updates
     */
    fun getNotificationByIdFlow(notificationId: String): Flow<Notification?> = 
        notificationDao.getNotificationByIdFlow(notificationId)
    
    /**
     * Insert a new notification
     */
    suspend fun insertNotification(notification: Notification) = 
        notificationDao.insertNotification(notification)
    
    /**
     * Update an existing notification
     */
    suspend fun updateNotification(notification: Notification) = 
        notificationDao.updateNotification(notification)
    


    /**
     * Get count of notifications for a specific service
     */
    suspend fun getNotificationCountByService(serviceId: String): Int = 
        notificationDao.getNotificationCountByService(serviceId)

    /**
     * Check if a notification exists by ID
     */
    suspend fun notificationExists(notificationId: String): Boolean = 
        notificationDao.notificationExists(notificationId)
}
