package so.appio.app.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.combine
import java.util.Date

private val Context.deviceDataStore: DataStore<Preferences> by preferencesDataStore(name = "device_preferences")

/**
 * DeviceDataStore manages device information using Jetpack DataStore.
 *
 * This class stores device-specific information including hardware details,
 * notification settings, and sync timestamps.
 */
class DeviceDataStore(private val context: Context) {

    companion object {
        // DataStore preference keys
        private val KEY_ID = stringPreferencesKey("device_id")
        private val KEY_NAME = stringPreferencesKey("device_name")
        private val KEY_OS_VERSION = stringPreferencesKey("device_os_version")
        private val KEY_DEVICE_IDENTIFIER = stringPreferencesKey("device_device_identifier")
        private val KEY_MODEL = stringPreferencesKey("device_model") // format: `${manufacturer}┼{model}` DeviceInfoManager.getManufacturerModel()
        private val KEY_NOTIFICATION_ENABLED = booleanPreferencesKey("device_notification_enabled")
        private val KEY_DEVICE_TOKEN = stringPreferencesKey("device_device_token")
        private val KEY_LAST_SYNC = longPreferencesKey("device_last_sync")
        private val KEY_LAST_UPDATE = longPreferencesKey("device_last_update")
    }

    // Device information flows
    val deviceId: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_ID] }

    val deviceName: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_NAME] }

    val osVersion: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_OS_VERSION] }

    val deviceIdentifier: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_DEVICE_IDENTIFIER] }

    // format: `${manufacturer}┼{model}` DeviceInfoManager.getManufacturerModel()
    val model: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_MODEL] }

    val notificationEnabled: Flow<Boolean> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_NOTIFICATION_ENABLED] ?: false }

    val deviceToken: Flow<String?> = context.deviceDataStore.data
        .map { preferences -> preferences[KEY_DEVICE_TOKEN] }

    val lastSync: Flow<Date?> = context.deviceDataStore.data
        .map { preferences -> 
            preferences[KEY_LAST_SYNC]?.let { Date(it) }
        }

    val lastUpdate: Flow<Date?> = context.deviceDataStore.data
        .map { preferences -> 
            preferences[KEY_LAST_UPDATE]?.let { Date(it) }
        }

    val isOutOfSync: Flow<Boolean> = lastSync.combine(lastUpdate) { sync, update ->
        if (sync != null && update != null) update.after(sync) else false
    }

    // Update methods
    suspend fun updateDeviceInfo(
        name: String,
        osVersion: String,
        deviceIdentifier: String,
        model: String, // format: `${manufacturer}┼{model}` DeviceInfoManager.getManufacturerModel()
    ) {
        context.deviceDataStore.edit { preferences ->
            preferences[KEY_NAME] = name
            preferences[KEY_OS_VERSION] = osVersion
            preferences[KEY_DEVICE_IDENTIFIER] = deviceIdentifier
            preferences[KEY_MODEL] = model
            preferences[KEY_LAST_UPDATE] = Date().time
        }
    }

    suspend fun updateDeviceId(id: String) {
        val currentValue = deviceId.first()
        if (currentValue != id) {
            context.deviceDataStore.edit { preferences ->
                preferences[KEY_ID] = id
                preferences[KEY_LAST_UPDATE] = Date().time
            }
        }
    }

    suspend fun updateNotificationEnabled(enabled: Boolean) {
        context.deviceDataStore.edit { preferences ->
            preferences[KEY_NOTIFICATION_ENABLED] = enabled
            preferences[KEY_LAST_UPDATE] = Date().time
        }
    }

    suspend fun updateDeviceToken(token: String) {
        context.deviceDataStore.edit { preferences ->
            preferences[KEY_DEVICE_TOKEN] = token
            preferences[KEY_LAST_UPDATE] = Date().time
        }
    }

    suspend fun updateLastSync(date: Date = Date()) {
        context.deviceDataStore.edit { preferences ->
            preferences[KEY_LAST_SYNC] = date.time
        }
    }

    suspend fun exists(): Boolean {
        return try {
            val deviceId = deviceId.first()
            deviceId != null
        } catch (_: Exception) {
            false
        }
    }
}
