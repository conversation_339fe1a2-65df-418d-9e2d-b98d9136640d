package so.appio.app.data.database.migrations

import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase

/**
 * Migration from database version 1 to version 2.
 * 
 * Changes:
 * - Add text_color column to services table (nullable string)
 * - Add background_color column to services table (nullable string)
 */
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(database: SupportSQLiteDatabase) {
        // Add text_color column to services table
        database.execSQL("ALTER TABLE services ADD COLUMN text_color TEXT")
        
        // Add background_color column to services table
        database.execSQL("ALTER TABLE services ADD COLUMN background_color TEXT")
    }
}
