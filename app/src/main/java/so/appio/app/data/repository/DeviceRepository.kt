package so.appio.app.data.repository

import android.content.Context
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import so.appio.app.data.DeviceDataStore
import so.appio.app.network.APIService

/**
 * Repository for device data management and synchronization.
 * Handles automatic sync when device data changes or goes out of sync.
 * Follows Repository pattern with Flow-based observation.
 */
class DeviceRepository(
    private val context: Context,
    private val apiService: APIService = APIService
) {
    companion object {
        private const val TAG = "LOG:DeviceRepository"
    }

    private val deviceDataStore = DeviceDataStore(context)
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    /**
     * Initialize the repository.
     * No Flow observation needed since we use immediate sync on updates.
     */
    fun initialize() {
        Log.d(TAG, "DeviceRepository initialized successfully")
    }

    /**
     * Get the DeviceDataStore instance.
     */
    fun getDeviceDataStore(): DeviceDataStore = deviceDataStore

    /**
     * Update notification enabled status and sync with server only if value changed.
     */
    suspend fun updateNotificationEnabled(enabled: Boolean) {
        val currentValue = deviceDataStore.notificationEnabled.first()
        if (currentValue != enabled) {
            Log.d(TAG, "Notification enabled changed from $currentValue to $enabled, updating and syncing")
            deviceDataStore.updateNotificationEnabled(enabled)
            syncDeviceWithServer()
        }
    }

    /**
     * Update device token and sync with server only if value changed.
     */
    suspend fun updateDeviceToken(token: String) {
        val currentValue = deviceDataStore.deviceToken.first()
        if (currentValue != token) {
            Log.d(TAG, "Device token changed, updating and syncing")
            deviceDataStore.updateDeviceToken(token)
            syncDeviceWithServer()
        }
    }

    /**
     * Check if device data is out of sync and sync with server if needed.
     */
    fun checkAndSyncIfNeeded() {
        scope.launch {
            try {
                val isOutOfSync = deviceDataStore.isOutOfSync.first()
                if (isOutOfSync) {
                    Log.d(TAG, "Device data is out of sync, triggering sync")
                    syncDeviceWithServer()
                } else {
                    Log.d(TAG, "Device data is in sync")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error checking sync status", e)
            }
        }
    }

    /**
     * Sync device data with server via API call.
     * Updates last sync timestamp on successful API call.
     */
    private suspend fun syncDeviceWithServer() {
        try {
            val deviceId = deviceDataStore.deviceId.first()
            if (deviceId != null) {
                Log.d(TAG, "Syncing device with server: $deviceId")
                val success = apiService.updateDevice(deviceId, deviceDataStore)
                if (success) {
                    deviceDataStore.updateLastSync()
                    Log.d(TAG, "Device sync successful")
                } else {
                    Log.w(TAG, "Device sync failed")
                }
            } else {
                Log.w(TAG, "No device ID found, skipping sync")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing device with server", e)
        }
    }
}
