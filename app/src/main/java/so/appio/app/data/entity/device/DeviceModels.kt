package so.appio.app.data.entity.device

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request models for device-related API operations
 */

@Serializable
data class NewDeviceRequest(
    @SerialName("customer_user_id")
    val customerUserId: String,

    @SerialName("name")
    val name: String,

    @SerialName("platform")
    val platform: String = "android",

    @SerialName("os_version")
    val osVersion: String,

    @SerialName("model")
    val model: String,

    @SerialName("notifications_enabled")
    val notificationsEnabled: Boolean = false,

    @SerialName("device_identifier")
    val deviceIdentifier: String = "",
)

@Serializable
data class UpdateDeviceData(
    @SerialName("notifications_enabled")
    val notificationsEnabled: Boolean,

    @SerialName("device_token")
    val deviceToken: String
)

@Serializable
data class UpdateDeviceRequest(
    @SerialName("data")
    val data: UpdateDeviceData
)

/**
 * Response models for device-related API operations
 */

@Serializable
data class DeviceResponse(val id: String)
