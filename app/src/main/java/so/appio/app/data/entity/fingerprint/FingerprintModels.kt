package so.appio.app.data.entity.fingerprint

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Network models for fingerprint-related API operations
 */

@Serializable
data class FingerprintRequest(
    @SerialName("user_agent")
    val userAgent: String,

    @SerialName("screen_resolution")
    val screenResolution: String,

    @SerialName("language")
    val language: String,

    @SerialName("time_offset")
    val timeOffset: Int
)

@Serializable
data class FingerprintResponse(
    @SerialName("fingerprint_id")
    var fingerprintId: String,

    @SerialName("service_id")
    var serviceId: String,

    @SerialName("customer_user_id")
    var customerUserId: String,
)
