package so.appio.app.data.entity.notification

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

/**
 * Notification entity representing a notification stored in the Room database.
 *
 * @param id Unique identifier for the notification (not auto-generated)
 * @param serviceId ID of the service this notification belongs to
 * @param title Notification title
 * @param body Notification body text
 * @param link Optional link URL associated with the notification
 * @param imageUrl Optional image URL for the notification
 * @param receivedAt Timestamp when the notification was received
 */
@Entity(tableName = "notifications")
data class Notification(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,

    @ColumnInfo(name = "service_id")
    val serviceId: String,

    @ColumnInfo(name = "title")
    val title: String,

    @ColumnInfo(name = "body")
    val body: String,

    @ColumnInfo(name = "link")
    var link: String? = null,

    @ColumnInfo(name = "image_url")
    var imageUrl: String? = null,

    @ColumnInfo(name = "received_at")
    val receivedAt: Date
)
