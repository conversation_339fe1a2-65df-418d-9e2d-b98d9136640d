package so.appio.app.data.entity.service

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import so.appio.app.data.entity.widget.WidgetResponse

/**
 * Request models for service-related API operations
 */

@Serializable
data class LinkServiceRequest(
    @SerialName("customer_user_id")
    val customerUserId: String
)

/**
 * Response models for service-related API operations
 */

@Serializable
data class ServiceResponse(
    @SerialName("id")
    val id: String,

    @SerialName("title")
    val title: String,

    @SerialName("description")
    val description: String? = null,

    @SerialName("logo_url")
    val logoURL: String,

    @SerialName("banner_url")
    val bannerURL: String? = null,

    @SerialName("url")
    val url: String? = null,

    @SerialName("text_color")
    val textColor: String? = null,

    @SerialName("background_color")
    val backgroundColor: String? = null,

    @SerialName("widgets")
    val widgets: List<WidgetResponse>? = null
)
