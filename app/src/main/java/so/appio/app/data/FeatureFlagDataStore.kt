package so.appio.app.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

private val Context.featureFlagDataStore: DataStore<Preferences> by preferencesDataStore(name = "feature_flag_preferences")

/**
 * FeatureFlagDataStore manages feature flag configuration using Jetpack DataStore.
 *
 * This class stores a single feature flag instance with id, version, and config.
 * Since there's only ever a single instance, the data can be overwritten.
 */
class FeatureFlagDataStore(private val context: Context) {

    companion object {
        private val KEY_ID = stringPreferencesKey("ff_id")
        private val KEY_VERSION = stringPreferencesKey("ff_version")
        private val KEY_CONFIG = stringPreferencesKey("ff_config")
    }

    // Feature flag data flows
    val featureFlagId: Flow<String?> = context.featureFlagDataStore.data
        .map { preferences -> preferences[KEY_ID] }

    val featureFlagVersion: Flow<String?> = context.featureFlagDataStore.data
        .map { preferences -> preferences[KEY_VERSION] }

    val featureFlagConfig: Flow<String?> = context.featureFlagDataStore.data
        .map { preferences -> preferences[KEY_CONFIG] }

    /**
     * Get all feature flag data as a combined flow
     */
    val featureFlag: Flow<FeatureFlagData?> = context.featureFlagDataStore.data
        .map { preferences ->
            val id = preferences[KEY_ID]
            val version = preferences[KEY_VERSION]
            val config = preferences[KEY_CONFIG]
            
            if (id != null && version != null && config != null) {
                FeatureFlagData(id, version, config)
            } else {
                null
            }
        }

    /**
     * Update the feature flag data. This will overwrite any existing data.
     */
    suspend fun updateFeatureFlag(id: String, version: String, config: String) {
        context.featureFlagDataStore.edit { preferences ->
            preferences[KEY_ID] = id
            preferences[KEY_VERSION] = version
            preferences[KEY_CONFIG] = config
        }
    }

    /**
     * Clear all feature flag data
     */
    suspend fun clearFeatureFlag() {
        context.featureFlagDataStore.edit { preferences ->
            preferences.clear()
        }
    }

    /**
     * Check if feature flag data exists
     */
    suspend fun hasFeatureFlag(): Flow<Boolean> = context.featureFlagDataStore.data
        .map { preferences ->
            preferences[KEY_ID] != null && 
            preferences[KEY_VERSION] != null && 
            preferences[KEY_CONFIG] != null
        }
}

/**
 * Data class representing feature flag information
 */
data class FeatureFlagData(
    val id: String,
    val version: String,
    val config: String
)
