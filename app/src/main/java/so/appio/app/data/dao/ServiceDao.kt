package so.appio.app.data.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import so.appio.app.data.entity.service.Service

/**
 * Data Access Object (DAO) for Service entity.
 * Provides methods to interact with the services table in the database.
 */
@Dao
interface ServiceDao {
    
    /**
     * Get all services as a Flow for reactive updates
     */
    @Query("SELECT * FROM services ORDER BY id DESC")
    fun getAllServices(): Flow<List<Service>>
    
    /**
     * Get all services as a list (one-time query)
     */
    @Query("SELECT * FROM services ORDER BY id DESC")
    suspend fun getAllServicesList(): List<Service>
    
    /**
     * Get a service by its ID
     */
    @Query("SELECT * FROM services WHERE id = :serviceId LIMIT 1")
    suspend fun getServiceById(serviceId: String): Service?
    
    /**
     * Get a service by its ID as Flow for reactive updates
     */
    @Query("SELECT * FROM services WHERE id = :serviceId LIMIT 1")
    fun getServiceByIdFlow(serviceId: String): Flow<Service?>
    
    /**
     * Insert a new service. Replace if conflict occurs.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertService(service: Service)
    
    /**
     * Update an existing service
     */
    @Update
    suspend fun updateService(service: Service)
    
    /**
     * Delete a service
     */
    @Delete
    suspend fun deleteService(service: Service)
    
    /**
     * Delete a service by its ID
     */
    @Query("DELETE FROM services WHERE id = :serviceId")
    suspend fun deleteServiceById(serviceId: String)
    
    /**
     * Update specific service fields (title, description, logoURL, bannerURL, url, textColor, backgroundColor)
     */
    @Query("UPDATE services SET title = :title, description = :description, logo_url = :logoURL, banner_url = :bannerURL, url = :url, text_color = :textColor, background_color = :backgroundColor WHERE id = :serviceId")
    suspend fun updateServiceFields(serviceId: String, title: String, description: String?, logoURL: String, bannerURL: String?, url: String?, textColor: String?, backgroundColor: String?)

    /**
     * Update notification status for a service
     */
    @Query("UPDATE services SET notifications_enabled = :enabled WHERE id = :serviceId")
    suspend fun updateNotificationStatus(serviceId: String, enabled: Boolean)
    
    /**
     * Get count of all services
     */
    @Query("SELECT COUNT(*) FROM services")
    suspend fun getServiceCount(): Int
    
    /**
     * Check if a service exists by ID
     */
    @Query("SELECT EXISTS(SELECT 1 FROM services WHERE id = :serviceId)")
    suspend fun serviceExists(serviceId: String): Boolean

    /**
     * Delete services that are NOT in the provided list of service IDs
     */
    @Query("DELETE FROM services WHERE id NOT IN (:serviceIds)")
    suspend fun deleteServicesNotInList(serviceIds: List<String>)
}
