package so.appio.app.data.entity.notification

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Response models for notification-related API operations
 */

@Serializable
data class NotificationPayload(
    @SerialName("title")
    val title: String,

    @SerialName("message")
    val message: String,

    @SerialName("link")
    val link: String? = null,

    @SerialName("imageUrl")
    val imageUrl: String? = null
)

@Serializable
data class NotificationResponse(
    @SerialName("id")
    val id: String,

    @SerialName("serviceId")
    val serviceId: String,

    @SerialName("payload")
    val payload: NotificationPayload
)
