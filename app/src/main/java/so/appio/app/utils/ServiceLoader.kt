package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import so.appio.app.data.entity.service.Service
import so.appio.app.network.APIError

/**
 * Utility class for common service loading operations.
 * Provides shared logic for device registration and service fetching.
 */
class ServiceLoader(
    private val context: Context
) {

    companion object {
        private const val TAG = "LOG:ServiceLoader"
    }

    private val deviceManager by lazy {
        val app = context.applicationContext as so.appio.app.MyApplication
        app.deviceManager
    }
    private val deviceDataStore by lazy {
        val app = context.applicationContext as so.appio.app.MyApplication
        app.deviceRepository.getDeviceDataStore()
    }
    private val serviceManager = ServiceManager(context)

    /**
     * Register or get device and fetch service data.
     * This is the common logic shared between fingerprint detection and URL processing.
     *
     * @param serviceId The service ID to fetch
     * @param customerUserId The customer user ID for device registration
     * @return Service entity if found and stored successfully, null otherwise
     */
    suspend fun registerDeviceAndFetchService(serviceId: String, customerUserId: String): Service? = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Registering device and fetching service - serviceId: $serviceId, customerUserId: $customerUserId")

            // Use ServiceRegistrationState to prevent race conditions with service refresh
            ServiceRegistrationState.withRegistrationInProgress {
                // Register or get device and link to service
                val resultDataStore = deviceManager.registerOrGetDevice(
                    serviceId = serviceId,
                    customerUserId = customerUserId,
                    deviceDataStore = deviceDataStore
                )
                Log.d(TAG, "Device linked to service successfully. ID: ${resultDataStore.deviceId.first()}")

                // return: Fetch and store service data with widgets
                val result = serviceManager.fetchAndStoreServiceWithWidgets(serviceId, customerUserId)
                if (result == null) {
                    Log.w(TAG, "Failed to fetch and store service with widgets for serviceId: $serviceId")
                    return@withRegistrationInProgress null
                }
                val (service, _) = result
                service
            }
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Service loading debounced, skipping")
            null
        } catch (e: Exception) {
            Log.e(TAG, "Error during service loading for serviceId: $serviceId", e)
            null
        }
    }
}
