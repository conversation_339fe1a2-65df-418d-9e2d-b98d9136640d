package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import so.appio.app.data.entity.service.Service
import so.appio.app.data.database.DatabaseManager
import so.appio.app.network.APIError

class FingerPrintManager(
    private val context: Context
) {

    companion object {
        private const val TAG = "LOG:FingerPrintManager"
    }

    private val deviceManager by lazy {
        val app = context.applicationContext as so.appio.app.MyApplication
        app.deviceManager
    }
    private val serviceLoader = ServiceLoader(context)

    /**
     * Handle normal app launch - check for fingerprints if no services available
     * This method encapsulates the entire normal launch logic including service checking
     *
     * @param onServiceFound Callback when a service is found (either existing or from fingerprint)
     * @param onComplete Callback when the operation is complete (success or failure)
     */
    suspend fun handleNormalLaunch(
        onServiceFound: (Service) -> Unit,
        onComplete: () -> Unit
    ) {
        Log.d(TAG, "Handling normal launch fingerprint check")

        try {
            // Check service count directly from database to avoid StateFlow timing issues
            val serviceCount = DatabaseManager.getServiceRepository().getServiceCount()
            Log.d(TAG, "Services in database: $serviceCount services")

            if (serviceCount > 0) {
                Log.d(TAG, "Services exist ($serviceCount), skipping fingerprint check")
                return
            }

            // No services exist - run fingerprint check
            Log.d(TAG, "No services available, running fingerprint check...")
            val service = check()
            if (service != null) {
                Log.d(TAG, "Fingerprint match found, navigating to service: ${service.title}")
                onServiceFound(service)
            } else {
                Log.d(TAG, "No fingerprint match found")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during normal launch handling", e)
        } finally {
            onComplete()
        }
    }

    /**
     * Check for fingerprint match, register device, and fetch service data if found
     * @return Service entity if fingerprint detected and service loaded successfully, null otherwise
     */
    suspend fun check(): Service? = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting fingerprint check")

            val fingerprintResponse = deviceManager.matchFingerprint()
            if (fingerprintResponse == null) {
                Log.d(TAG, "No fingerprint match found")
                return@withContext null
            }

            if (fingerprintResponse.serviceId.isEmpty() || fingerprintResponse.customerUserId.isEmpty()) {
                Log.d(TAG, "Fingerprint response has empty serviceId or customerUserId")
                return@withContext null
            }

            val service = serviceLoader.registerDeviceAndFetchService(
                serviceId = fingerprintResponse.serviceId,
                customerUserId = fingerprintResponse.customerUserId
            )

            return@withContext service
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Fingerprint match request debounced, skipping")
            return@withContext null
        } catch (e: Exception) {
            Log.e(TAG, "Error during fingerprint check", e)
            return@withContext null
        }
    }
}
