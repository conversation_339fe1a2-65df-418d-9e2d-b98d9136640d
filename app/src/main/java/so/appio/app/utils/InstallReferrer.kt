package so.appio.app.utils

import android.content.Context
import android.util.Log
import androidx.core.net.toUri
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails

object InstallReferrer {

    const val TAG = "LOG:InstallReferrer"

    // Callbacks
    lateinit var onValidUrl: ((String) -> Unit)
    lateinit var onComplete: () -> Unit

    // Finds referrer all the time, default: utm_source=google-play&utm_medium=organic
    fun detectInstallReferrer(context: Context) {
        Log.d(TAG, "Checking Install Referrer")

        val referrerClient = InstallReferrerClient.newBuilder(context).build()

        // example: https://play.google.com/store/apps/details?id=so.appio.app&referrer=service%3Ddemo_svc_01jvtpgft2ztw03dgdffvdx72w%26user%3D01JWDXJF3YM3FRFR1DQHGG2RM8%253Ahi%2540appio.so
        referrerClient.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                // NOTE: Do not use early returns, the code must reach: onComplete() at the end
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> {
                        try {
                            val response: ReferrerDetails = referrerClient.installReferrer

                            // Log all available referrer information
                            val referrerString = response.installReferrer

                            // if (response.referrerClickTimestampSeconds == 0L || response.installBeginTimestampSeconds == 0L)
                            if (referrerString.isEmpty() || !referrerString.contains("service=") || !referrerString.contains("user=")) {
                                Log.d(TAG, "No Appio referrer")
                            } else {
                                Log.d(TAG, "=== Install Referrer Details ===")
                                Log.d(TAG, "Referrer URL params: $referrerString")
                                Log.d(TAG, "Click Timestamp: $response.referrerClickTimestampSeconds (${formatTimestamp(response.referrerClickTimestampSeconds)})")
                                Log.d(TAG, "Install Timestamp: $response.installBeginTimestampSeconds (${formatTimestamp(response.installBeginTimestampSeconds)})")
                                Log.d(TAG, "Google Play Instant: $response.googlePlayInstantParam")

                                parseReferrerParameters(referrerString)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error retrieving install referrer details", e)
                        } finally {
                            // Clean up the connection
                            try {
                                referrerClient.endConnection()
                                Log.d(TAG, "Install Referrer client disconnected")
                            } catch (e: Exception) {
                                Log.w(TAG, "Error disconnecting Install Referrer client", e)
                            }
                        }
                    }
                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                        Log.w(TAG, "Install Referrer API not supported on this device")
                    }
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                        Log.e(TAG, "Install Referrer service unavailable - Google Play Store may not be available")
                    }
                    InstallReferrerClient.InstallReferrerResponse.DEVELOPER_ERROR -> {
                        Log.e(TAG, "Install Referrer API developer error - check implementation")
                    }
                    InstallReferrerClient.InstallReferrerResponse.SERVICE_DISCONNECTED -> {
                        Log.e(TAG, "Install Referrer service disconnected")
                    }
                    else -> {
                        Log.e(TAG, "Unknown Install Referrer response code: $responseCode")
                    }
                }

                onComplete()
            }

            override fun onInstallReferrerServiceDisconnected() {
                Log.w(TAG, "Install Referrer service disconnected")
                onComplete()
            }
        })
    }

    private fun formatTimestamp(timestampSeconds: Long): String {
        return if (timestampSeconds > 0) {
            val date = java.util.Date(timestampSeconds * 1000)
            java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(date)
        } else {
            "unknown"
        }
    }

    private fun parseReferrerParameters(referrerString: String) {
        Log.d(TAG, "Parsing referrer parameters from: $referrerString")

        try {
            // The referrer string is just query parameters (e.g., "utm_source=google&utm_campaign=test")
            // Uri.parse() needs a complete URL to properly parse query parameters
            // So we prepend a dummy scheme and host to make it a valid URL for parsing
            val uri = "https://example.com?$referrerString".toUri()

            // Extract specific parameters
            val serviceId = uri.getQueryParameter("service")
            val userId = uri.getQueryParameter("user")

            Log.d(TAG, "=== Referrer Parameters ===")
            Log.d(TAG, "Service ID: $serviceId")
            if (userId != null) {
                Log.d(TAG, "Customer User ID: $userId")
            }

            // Construct appio:// URL if we have a service ID
            if (serviceId != null) {
                val appioUrl = if (userId != null) {
                    "appio://appio/?service=$serviceId&user=$userId"
                } else {
                    "appio://appio/?service=$serviceId"
                }

                Log.d(TAG, "Constructed appio URL: $appioUrl")
                onValidUrl.invoke(appioUrl)
            } else {
                Log.d(TAG, "No service parameter found in referrer")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error parsing referrer parameters", e)
        }
    }
}