package so.appio.app.utils

import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * Utility functions for date formatting and manipulation
 */
object DateUtils {
    
    /**
     * Formats a date according to the app's standard format:
     * - Today: HH:mm (time only)
     * - Yesterday: "Yesterday"
     * - Other dates: "MMM dd" (e.g., "Jun 16")
     */
    fun formatDate(date: Date, wrap: Boolean = true): String {
        val calendar = Calendar.getInstance()
        val today = Calendar.getInstance()
        val yesterday = Calendar.getInstance().apply { add(Calendar.DAY_OF_YEAR, -1) }

        calendar.time = date

        return when {
            // Check if it's today
            calendar.get(Calendar.YEAR) == today.get(Calendar.YEAR) &&
            calendar.get(Calendar.DAY_OF_YEAR) == today.get(Calendar.DAY_OF_YEAR) -> {
                val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
                timeFormatter.format(date)
            }
            // Check if it's yesterday
            calendar.get(Calendar.YEAR) == yesterday.get(Calendar.YEAR) &&
            calendar.get(Calendar.DAY_OF_YEAR) == yesterday.get(Calendar.DAY_OF_YEAR) -> {
                val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
                "Yesterday" + (if (wrap) "\n" else " - ") + timeFormatter.format(date)
            }
            // For all other dates
            else -> {
                val dateFormatter = SimpleDateFormat("MMM dd" + (if (wrap) "\n" else " - " )+ "HH:mm", Locale.getDefault())
                dateFormatter.format(date)
            }
        }
    }
}
