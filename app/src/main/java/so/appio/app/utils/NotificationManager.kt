package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.notification.Notification
import so.appio.app.network.APIError
import so.appio.app.network.APIService
import java.util.Date

/**
 * NotificationManager handles notification-related operations including fetching notifications
 * from the API and storing them in the local database.
 */
class NotificationManager(
    private val context: Context,
    private val apiService: APIService = APIService
) {

    companion object {
        private const val TAG = "LOG:NotificationManager"
    }

    private val deviceDataStore by lazy {
        val app = context.applicationContext as so.appio.app.MyApplication
        app.deviceRepository.getDeviceDataStore()
    }
    private val notificationRepository = DatabaseManager.getNotificationRepository()

    /**
     * Fetches notifications for a specific service from the API and stores them in the database.
     *
     * @param serviceId The ID of the service to fetch notifications for
     */
    suspend fun fetchAndStoreNotifications(serviceId: String) = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Fetching and storing notifications for serviceId: $serviceId")

            val deviceId = deviceDataStore.deviceId.first()
            if (deviceId.isNullOrEmpty()) {
                Log.w(TAG, "No device ID available, skipping notification fetch for serviceId: $serviceId")
                return@withContext
            }

            val notificationResponses = fetchNotifications(serviceId, deviceId)
            storeNotifications(notificationResponses)

            Log.d(TAG, "Successfully fetched and stored notifications for serviceId: $serviceId")
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Fetch and store notifications debounced, skipping")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch and store notifications for serviceId: $serviceId", e)
        }
    }

    /**
     * Fetches notifications from the API only.
     *
     * @param serviceId The ID of the service to fetch notifications for
     * @param deviceId The device ID for authentication
     * @param cursor Optional cursor for pagination (default: empty for all notifications)
     * @return List of notification responses from the API
     * @throws Exception if API call fails
     */
    private suspend fun fetchNotifications(
        serviceId: String, 
        deviceId: String, 
        cursor: String = ""
    ) = apiService.fetchAllNotifications(serviceId, deviceId, cursor)

    /**
     * Stores notification responses in the local database.
     *
     * @param notificationResponses The notification responses from API to store
     * @throws Exception if database operations fail
     */
    private suspend fun storeNotifications(
        notificationResponses: List<so.appio.app.data.entity.notification.NotificationResponse>
    ) {
        notificationResponses.forEach { response ->
            try {
                val notification = Notification(
                    id = response.id,
                    serviceId = response.serviceId,
                    title = response.payload.title,
                    body = response.payload.message,
                    link = response.payload.link,
                    imageUrl = response.payload.imageUrl,
                    receivedAt = Date() // Using current time since API doesn't provide receivedAt
                )

                notificationRepository.insertNotification(notification)
                Log.d(TAG, "Stored notification: ${notification.title} (${notification.id})")
            } catch (e: Exception) {
                Log.e(TAG, "Failed to store notification: ${response.id}", e)
            }
        }
    }

    /**
     * Checks if a notification exists in the database.
     *
     * @param notificationId The ID of the notification to check
     * @return true if notification exists, false otherwise
     */
    suspend fun notificationExists(notificationId: String): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            notificationRepository.notificationExists(notificationId)
        } catch (e: Exception) {
            Log.e(TAG, "Error checking if notification exists: $notificationId", e)
            false
        }
    }

    /**
     * Gets the count of notifications for a specific service.
     *
     * @param serviceId The ID of the service
     * @return Number of notifications for the service
     */
    suspend fun getNotificationCount(serviceId: String): Int = withContext(Dispatchers.IO) {
        return@withContext try {
            notificationRepository.getNotificationCountByService(serviceId)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting notification count for serviceId: $serviceId", e)
            0
        }
    }

    /**
     * Gets a notification by its ID.
     *
     * @param notificationId The ID of the notification to retrieve
     * @return The notification if found, null otherwise
     */
    suspend fun getNotificationById(notificationId: String): Notification? = withContext(Dispatchers.IO) {
        return@withContext try {
            notificationRepository.getNotificationById(notificationId)
        } catch (e: Exception) {
            Log.e(TAG, "Error getting notification by ID: $notificationId", e)
            null
        }
    }


}
