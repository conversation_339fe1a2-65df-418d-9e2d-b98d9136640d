package so.appio.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.Log
import androidx.core.graphics.createBitmap
import androidx.core.graphics.toColorInt
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.CoroutineScope
import kotlinx.serialization.json.Json
import so.appio.app.data.entity.widget.WidgetElement
import so.appio.app.data.entity.widget.WidgetTemplate
import so.appio.app.ui.widgets.parseGaugeProperties
import so.appio.app.ui.widgets.convertColor
import java.io.FileOutputStream
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.JsonObject
import so.appio.app.ui.widgets.parseImageProperties

/**
 * Manager for parsing and pre-caching images from widget configurations.
 * This handles extracting image URLs from widget JSON config and caching them in the background.
 */
object WidgetImageManager {
    private const val TAG = "LOG:WidgetImageManager"

    /**
     * Pre-process widget configuration by pre-caching images and creating Gauge images
     * @param context The context to access cache directory
     * @param widgetConfig Widget configuration JSON string
     * @return updated widgetConfig string
     */
    fun preProcessConfig(context: Context, widgetConfig: String) : String {
        try {
            val template = Json.decodeFromString<WidgetTemplate>(widgetConfig)

            template.variants.forEach { variant ->
                processElements(variant.elements, context)
            }
            return Json.encodeToString(template)
        } catch (e: Exception) {
            Log.e(TAG, "Decoding widget config template failed: ${e.message} \n $widgetConfig")
            return ""
        }
    }

    /**
     * Recursively process a list of widget elements, pre-caching images and creating Gauge images
     * @param elements List of widget elements
     * @param context The context to access cache directory
     */
    private fun processElements(elements: List<WidgetElement>, context: Context) {
        elements.forEach { element ->
            if (element.type == "image") {
                val imageProperties = parseImageProperties(element.properties)
                if (imageProperties != null) {
                    val src = imageProperties.src
                    if (!src.isNullOrEmpty()) {
                        preCacheWidgetImage(src)
                        val filename = ImageCacheManager.generateHashedFilename(src)
                        val updatedProperties = element.properties.toMutableMap().apply {
                            put("__src", JsonPrimitive(filename))
                        }
                        element.properties = JsonObject(updatedProperties)
                    }
                }
            }

            if (element.type == "gauge") {
                val gaugeProperties = parseGaugeProperties(element.properties)
                Log.d(TAG, "Found gauge element with properties: $gaugeProperties")
                if (gaugeProperties != null) {
                    val color = convertColor(gaugeProperties.tint)

                    Log.d(TAG, "Gauge tint color: ${gaugeProperties.tint} -> $color")

                    val progress = (gaugeProperties.value ?: 0f) / 100f
                    val filename = renderGaugeBitmap(context, progress, color)
                    val updatedProperties = element.properties.toMutableMap().apply {
                        put("__src", JsonPrimitive(filename))
                    }
                    element.properties = JsonObject(updatedProperties)
                }
            }

            // Recursively process child elements if any
            if (element.elements.isNotEmpty()) {
                processElements(element.elements, context)
            }
        }
    }

    /**
     * Render a gauge bitmap and save it to cache
     * @param context The context to access cache directory
     * @param progress The progress of the gauge (0.0 to 1.0)
     * @param color The color of the gauge
     * @param size The size of the gauge in pixels
     * @return The filename of the saved bitmap, or null if failed
     */
    private fun renderGaugeBitmap(
        context: Context,
        progress: Float, // within: 0.0 to 1.0
        circleColor: Int = Color.BLUE,
        bgCircleColor: Int = Color.LTGRAY,
        size: Int = 400 // in pixels
    ): String? {
        if (progress < 0 || progress > 1) {
            return null
        }

        try {
            val bitmap = createBitmap(size, size)
            val canvas = Canvas(bitmap)

            val strokeWidthVal = size * 0.1f
            val centerX = size / 2f
            val centerY = size / 2f
            val radius = (size - strokeWidthVal) / 2f

            val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
                style = Paint.Style.STROKE
                strokeWidth = strokeWidthVal
                strokeCap = Paint.Cap.ROUND
            }

            // Background circle
            paint.color = bgCircleColor
            canvas.drawCircle(centerX, centerY, radius, paint)

            // Foreground arc (gauge)
            paint.color = circleColor
            val rect = RectF(
                centerX - radius,
                centerY - radius,
                centerX + radius,
                centerY + radius
            )
            canvas.drawArc(rect, -90f, 360 * progress, false, paint)

            // Random filename to avoid collisions
            val filename = java.util.UUID.randomUUID().toString()
            val file = ImageCacheManager.getCachedImageFile(context, filename)
            FileOutputStream(file).use {
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, it)
            }
            Log.d(TAG, "Rendered gauge bitmap and saved to: ${file.absolutePath}")

            return filename
        } catch (e: Exception) {
            Log.e(TAG, "Failed to render gauge bitmap: ${e.message}", e)
            return null
        }
    }

//    /**
//     * Get the cached image file for a given hashed filename
//     */
//    private fun getGaugeImageFile(context: Context, filename: String): File {
//        return File(getGaugeCacheDir(context), filename)
//    }

//    /**
//     * Get the dedicated directory for persistent gauge image storage
//     * Images are stored in: /data/data/so.appio.app/cache/gauge_cache/
//     * The system will automatically delete files in this directory as disk space is needed
//     */
//    private fun getGaugeCacheDir(context: Context): File {
//        val cacheDir = File(context.cacheDir, GAUGE_CACHE_DIRECTORY)
//        if (!cacheDir.exists()) {
//            cacheDir.mkdirs()
//        }
//        return cacheDir
//    }

    /**
     * Pre-cache widget image in the background
     * @param src The source URL of the image
     */
    private fun preCacheWidgetImage(src: String) {
        // Launch on IO dispatcher to run in background
        CoroutineScope(Dispatchers.IO).launch {
            ImageCacheManager.preCacheImage(src)
            Log.d(TAG, "Completed pre-caching $src")
        }
    }
}




// number
const val TMP_JSON_NUMBER = """
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"width": 250,
				"height": 250,
				"background": "clear",
                "url": "https://appio.so"
			},
			"elements": [
                {
                    "type": "column",
                    "properties": {
                        "height": "max",
                        "width": "max",
                        "horizontalAlignment": "center",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "lastUpdated",
                            "properties": {
                                "width": "max",
                                "alignment": "end",
                                "fontSize": 10
                            }
                        },
                        {
                            "type": "text",
                            "properties": {
                                "text": "75",
                                "fontSize": 30,
                                "fontWeight": "bold",
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton"
                        }
                    ]
				}
			]
		}
	]
}
"""

// ring
const val TMP_JSON_RING = """
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"width": 250,
				"height": 250,
				"background": "clear",
                "url": "https://appio.so"
			},
			"elements": [
                {
                    "type": "column",
                    "properties": {
                        "height": "max",
                        "width": "max",
                        "horizontalAlignment": "center",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "lastUpdated",
                            "properties": {
                                "width": "max",
                                "alignment": "end",
                                "color": "secondary",
                                "fontSize": 10
                            }
                        },
                        {
                            "type": "gauge",
                            "properties": {
                                "value": 75,
                                "label": "75",
                                "color": "primary",
                                "tint": "primary",
                                "fontSize": 20,
                                "size": 50,
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton"
                        }
                    ]
				}
			]
		}
	]
}
"""

//const val TMP_JSON_2 = """
//{
//    "variants": [
//        {
//            "properties": {
//                "version": "1.0",
//                "width": 250,
//                "height": 250,
//                "background": "clear"
//            },
//            "elements": [
//                {
//                    "type": "column",
//                    "properties": {
//                    },
//                    "elements": [
//                        {
//                            "type": "row",
//                            "properties": {
//                            },
//                            "elements": [
//                                {
//                                    "type": "text",
//                                    "properties": {
//                                        "text": "Hello"
//                                    }
//                                },
//                                {
//                                    "type": "text",
//                                    "properties": {
//                                        "text": "World"
//                                    }
//                                }
//                            ]
//                        },
//                        {
//                            "type": "text",
//                            "properties": {
//                                "text": "Test ABC"
//                            }
//                        }
//                    ]
//                }
//            ]
//        }
//    ]
//}
//"""

const val TMP_JSON_3 = """
{
    "variants": [
        {
            "properties": {
                "version": "1.0",
                "width": 250,
                "height": 250,
                "background": "clear"
            },
            "elements": [
                {
                    "type": "row",
                    "properties": {
                        "background": "red",
                        "width": "max",
                        "height": "max",
                        "horizontalAlignment": "start",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "column",
                            "properties": {
                                "background": "green",
                                "height": "max",
                                "horizontalAlignment": "center",
                                "verticalAlignment": "center"
                            },
                            "elements": [
                                {
                                    "type": "image",
                                    "properties": {
                                        "src": "https://picsum.photos/300?111",
                                        "width": 40
                                    }
                                },
                                {
                                    "type": "gauge",
                                    "properties": {
                                        "value": 22,
                                        "color": "#00ffaa",
                                        "label": "22%",
                                        "tint": "pink",
                                        "fontSize": 22,
                                        "fontWeight": "bold"
                                    }
                                }
                            ]
                        },
                        {
                            "type": "column",
                            "properties": {
                            },
                            "elements": [
                                {
                                    "type": "text",
                                    "properties": {
                                        "text": "Testing content 123",
                                        "fontWeight": "semibold",
                                        "color": "primary",
                                        "alignment": "center",
                                        "padding": {
                                            "top": 10,
                                            "bottom": 10
                                        }
                                    }
                                },
                                {
                                    "type": "refreshButton",
                                    "properties": {
                                        "color": "white",
                                        "tint": "yellow"
                                    }
                                },
                                {
                                    "type": "lastUpdated",
                                    "properties": {
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}
"""

//const val TMP_JSON = TMP_JSON_3
const val TMP_JSON = TMP_JSON_RING
//const val TMP_JSON = TMP_JSON_NUMBER