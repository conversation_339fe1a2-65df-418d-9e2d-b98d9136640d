package so.appio.app.utils

import android.content.Context
import android.util.Log
import androidx.work.CoroutineWorker
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.ExistingPeriodicWorkPolicy
import java.util.concurrent.TimeUnit

/**
 * Background worker that periodically refreshes all services and widgets data.
 * This ensures the app data stays fresh even when the app is not actively used.
 */
class ServiceRefreshWorker(
    context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {

    companion object {
        private const val TAG = "LOG:ServiceRefreshWorker"
        private const val REFRESH_INTERVAL_MINUTES = 30L // Refresh every 30 minutes
        private const val ONE_TIME_WORK_NAME = "ServiceRefresh"
        private const val PERIODIC_WORK_NAME = "ServicePeriodicRefresh"
        private const val KEY_REFRESH_REASON = "refresh_reason"

        // Refresh reasons for debugging and analytics
        const val REASON_APP_RESUME = "app_resume"
        const val REASON_PERIODIC = "periodic"
        const val REASON_MANUAL = "manual"

        /**
         * Start continuous background refresh (main refresh strategy)
         */
        fun startAutoRefresh(context: Context) {
            Log.d(TAG, "Starting service auto-refresh (every $REFRESH_INTERVAL_MINUTES minutes)")
            val inputData = Data.Builder()
                .putString(KEY_REFRESH_REASON, REASON_PERIODIC)
                .build()

            val periodicWorkRequest = PeriodicWorkRequestBuilder<ServiceRefreshWorker>(
                REFRESH_INTERVAL_MINUTES, TimeUnit.MINUTES
            )
                .setInputData(inputData)
                .addTag(PERIODIC_WORK_NAME)
                .build()

            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                PERIODIC_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP, // Keep existing if already running
                periodicWorkRequest
            )
        }

        /**
         * Trigger immediate service refresh for specific events
         */
        fun refreshServicesForEvent(context: Context, reason: String) {
            Log.d(TAG, "Triggering immediate service refresh for reason: $reason")

            val inputData = Data.Builder()
                .putString(KEY_REFRESH_REASON, reason)
                .build()

            val workRequest = OneTimeWorkRequestBuilder<ServiceRefreshWorker>()
                .setInputData(inputData)
                .addTag(ONE_TIME_WORK_NAME)
                .build()

            // Use REPLACE to avoid duplicate immediate work, but don't interfere with periodic updates
            WorkManager.getInstance(context).enqueueUniqueWork(
                ONE_TIME_WORK_NAME,
                ExistingWorkPolicy.REPLACE,
                workRequest
            )
        }

        fun stopAutoRefresh(context: Context) {
            Log.d(TAG, "Stopping service auto-refresh")
            // Cancel the periodic updates
            WorkManager.getInstance(context).cancelUniqueWork(PERIODIC_WORK_NAME)
            // Cancel any pending immediate event-driven updates
            WorkManager.getInstance(context).cancelUniqueWork(ONE_TIME_WORK_NAME)
        }
    }

    override suspend fun doWork(): Result {
        val refreshReason = inputData.getString(KEY_REFRESH_REASON) ?: REASON_PERIODIC
        Log.d(TAG, "Refreshing all services for reason: $refreshReason")

        // Check if service registration is in progress to prevent race condition
        if (ServiceRegistrationState.isRegistrationInProgress()) {
            Log.d(TAG, "Service registration in progress, skipping refresh to prevent race condition")
            return Result.success() // Return success to avoid retries
        }

        try {
            val serviceManager = ServiceManager(applicationContext)
            val success = serviceManager.refreshAllServices()

            return if (success) {
                Log.d(TAG, "Service refresh completed successfully")
                Result.success()
            } else {
                Log.w(TAG, "Service refresh completed with some failures")
                Result.failure()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error refreshing services", e)
            return Result.failure()
        }
    }
}
