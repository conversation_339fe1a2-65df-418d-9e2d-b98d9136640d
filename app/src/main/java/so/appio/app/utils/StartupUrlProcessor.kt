package so.appio.app.utils

import android.content.Context
import android.util.Log
import so.appio.app.data.entity.service.Service

/**
 * Centralized handler for all URL processing during app startup.
 * Determines whether to present ServiceScreen based on URL validation.
 *
 * Triggered by Intents, Install Referrer, and QR Scanner.
 */
class StartupUrlProcessor(
    private val serviceLoader: ServiceLoader
) {

    companion object {
        private const val TAG = "LOG:StartupUrlProcessor"

        @Volatile
        private var INSTANCE: StartupUrlProcessor? = null

        /**
         * Get singleton instance of StartupUrlProcessor
         */
        fun getInstance(context: Context): StartupUrlProcessor {
            return INSTANCE ?: synchronized(this) {
                val instance = StartupUrlProcessor(
                    serviceLoader = ServiceLoader(context)
                )
                INSTANCE = instance
                instance
            }
        }
    }

    /**
     * Processes a URL to determine the appropriate startup action.
     *
     * @param data The URL string to process
     * @return Service entity if found, null otherwise
     */
    suspend fun processUrl(data: String): Service? {
        Log.d(TAG, "Processing URL: $data")
        
        if (data.isBlank()) {
            Log.d(TAG, "Empty URL provided, returning NoAction")
            return null
        }
        
        // Validate and parse the URL using existing UrlValidator
        return when (val result = UrlValidator.validateAndParseUrl(data)) {
            is UrlValidationResult.Success -> {
                Log.d(TAG, "Valid URL found - serviceId: ${result.params.serviceId}, customerUserId: ${result.params.customerUserId}")

                // return: Register device and fetch service
                serviceLoader.registerDeviceAndFetchService(
                    serviceId = result.params.serviceId,
                    customerUserId = result.params.customerUserId ?: ""
                )
            }
            is UrlValidationResult.Error -> {
                Log.e(TAG, "Invalid URL: ${result.message}")
                return null
            }
        }
    }
}
