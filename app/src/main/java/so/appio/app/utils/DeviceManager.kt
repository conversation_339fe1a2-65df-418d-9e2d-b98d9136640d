package so.appio.app.utils

import android.content.Context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import so.appio.app.data.DeviceDataStore
import so.appio.app.data.entity.fingerprint.FingerprintRequest
import so.appio.app.data.entity.fingerprint.FingerprintResponse
import so.appio.app.network.APIService
import java.util.Locale
import java.util.TimeZone

/**
 * DeviceManager handles device registration and fingerprinting.
 * Focused on device-related operations without sync responsibilities.
 */
class DeviceManager(
    private val context: Context,
    private val apiService: APIService = APIService
) {
    private val deviceInfoManager = DeviceInfoManager(context)

    // Match fingerprint using detailed fingerprint request built from DeviceInfoManager
    suspend fun matchFingerprint(): FingerprintResponse? = withContext(Dispatchers.IO) {
        val fingerprintRequest = FingerprintRequest(
            userAgent =  "unknown", // We don't know what is the default browser
            screenResolution = deviceInfoManager.getScreenResolution(), // not reliable
            language = Locale.getDefault().toLanguageTag(),
            timeOffset = (TimeZone.getDefault().getOffset(System.currentTimeMillis()) / -60000), // minutes offset
        )
        apiService.fingerprintMatch(fingerprintRequest)
    }

    // Register and Link device using DeviceInfo from DeviceInfoManager, then update DataStore
    suspend fun registerDevice(serviceId: String, customerUserId: String, deviceDataStore: DeviceDataStore) = withContext(Dispatchers.IO) {
        val deviceInfo = deviceInfoManager.collectDeviceInfo()
        val response = apiService.registerDevice(serviceId, customerUserId, deviceInfo)
        deviceDataStore.updateDeviceId(response.id)
        deviceDataStore.updateDeviceInfo(
            name = deviceInfo.name,
            osVersion = deviceInfo.osVersion,
            deviceIdentifier = deviceInfo.deviceIdentifier,
            model = deviceInfo.manufacturerModel,
        )
        deviceDataStore
    }

    // Link device to service
    private suspend fun linkDeviceService(deviceId: String, serviceId: String, customerUserId: String): Boolean =
        apiService.linkService(deviceId, serviceId, customerUserId)

    /**
     * Register or get device and link to service.
     * First checks if device ID exists in DataStore, if not registers a new device.
     * Then links the device to the service.
     *
     * @param serviceId The service ID to link to
     * @param customerUserId The customer user ID
     * @param deviceDataStore The DeviceDataStore instance to use
     * @return DeviceDataStore instance
     */
    suspend fun registerOrGetDevice(serviceId: String, customerUserId: String, deviceDataStore: DeviceDataStore): DeviceDataStore = withContext(Dispatchers.IO) {
        val existingDeviceId = deviceDataStore.deviceId.first()
        if (existingDeviceId != null) {
            linkDeviceService(existingDeviceId, serviceId, customerUserId)
            return@withContext deviceDataStore
        }

        val newDevice = registerDevice(serviceId, customerUserId, deviceDataStore)
        return@withContext newDevice
    }
}
