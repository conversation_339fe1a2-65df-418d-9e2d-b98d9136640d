package so.appio.app.utils

import android.app.PendingIntent
import android.appwidget.AppWidgetManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.util.Log
import so.appio.app.widgets.AppioWidgetConfigActivity
import so.appio.app.widgets.AppioWidgetReceiver
import androidx.core.content.edit
import android.app.Activity
import android.os.Handler
import android.os.Looper

/**
 * Manager for handling widget discovery functionality.
 * 
 * Provides methods to check widget pinning support and request widget placement
 * on the home screen using Android's widget discovery API.
 */
class WidgetDiscoveryManager(private val context: Context) {
    
    companion object {
        private const val TAG = "LOG:WidgetDiscoveryManager"
        const val PREFS_NAME = "widget_discovery"
        const val KEY_PENDING_SERVICE_ID = "pending_service_id"
        const val KEY_PENDING_TIMESTAMP = "pending_timestamp"

        /**
         * Get the pending service ID for widget discovery and clear it
         */
        fun getPendingServiceId(context: Context): String? {
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            val serviceId = prefs.getString(KEY_PENDING_SERVICE_ID, null)
            if (serviceId != null) {
                // Clear it after reading
                prefs.edit { remove(KEY_PENDING_SERVICE_ID) }
                Log.d(TAG, "Retrieved and cleared pending service ID: $serviceId")
            }
            return serviceId
        }

        /**
         * Call this when a widget was added to homescreen
         */
        fun onWidgetAdded(context: Context) {
            Log.d(TAG, "Widget actually added - exiting app")
            Log.d(TAG, "Context type: ${context.javaClass.simpleName}")

            Handler(Looper.getMainLooper()).postDelayed({
                try {
                    // Try multiple approaches to move app to background
                    if (context is Activity) {
                        Log.d(TAG, "Using Activity.moveTaskToBack()")
                        context.moveTaskToBack(true)
                    } else {
                        Log.d(TAG, "Context is not Activity, trying alternative approach")

                        // Another alternative: Start home intent
                        val homeIntent = Intent(Intent.ACTION_MAIN).apply {
                            addCategory(Intent.CATEGORY_HOME)
                            flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        }
                        context.startActivity(homeIntent)
                        Log.d(TAG, "Started home intent to move app to background")
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error moving app to background", e)
                }
            }, 100)
        }
    }
    
    /**
     * Check if the current launcher supports widget pinning.
     * Widget pinning is only available on Android 8.0 (API 26) and above.
     */
    fun isWidgetPinningSupported(): Boolean {
        val appWidgetManager = AppWidgetManager.getInstance(context)
        return appWidgetManager.isRequestPinAppWidgetSupported
    }
    
    /**
     * Request to pin a widget to the home screen for the specified service.
     *
     * @param serviceId The ID of the service to pre-configure the widget for
     * @return true if the request was successfully initiated, false otherwise
     */
    fun requestPinWidget(serviceId: String): Boolean {
        if (!isWidgetPinningSupported()) {
            Log.w(TAG, "Widget pinning not supported on this device/launcher")
            return false
        }

        return try {
            val appWidgetManager = AppWidgetManager.getInstance(context)

            // Store the pending service ID for when the widget is added
            val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
            prefs.edit {
                putString(KEY_PENDING_SERVICE_ID, serviceId)
                putLong(KEY_PENDING_TIMESTAMP, System.currentTimeMillis())
            }
            Log.d(TAG, "Stored pending service ID: $serviceId with timestamp")

            // Create component name for our widget provider
            val widgetProvider = ComponentName(context, AppioWidgetReceiver::class.java)

            // Create pending intent to automatically open configuration after widget is added
            // Note: Android will add EXTRA_APPWIDGET_ID to this intent when the widget is created
            val configIntent = Intent(context, AppioWidgetConfigActivity::class.java).apply {
                action = AppWidgetManager.ACTION_APPWIDGET_CONFIGURE
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or
                       Intent.FLAG_ACTIVITY_CLEAR_TOP or
                       Intent.FLAG_ACTIVITY_EXCLUDE_FROM_RECENTS or
                       Intent.FLAG_ACTIVITY_NO_HISTORY
            }
            val configPendingIntent = PendingIntent.getActivity(
                context,
                System.currentTimeMillis().toInt(), // Use unique request code
                configIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_MUTABLE // Use MUTABLE so Android can add the widget ID
            )

            // Request widget pinning with automatic configuration
            // Note: Doesn't return Glance Widget ID and there is no way to find out what ID was created
            val success = appWidgetManager.requestPinAppWidget(
                widgetProvider,
                null, // No initial configuration bundle
                configPendingIntent  // Automatically open config after widget is added
            )

            if (success) {
                Log.d(TAG, "Widget pin request initiated successfully for serviceId: $serviceId")
                Log.d(TAG, "Configuration PendingIntent created with action: ${configIntent.action}")
                Log.d(TAG, "Configuration PendingIntent flags: ${configIntent.flags}")
            } else {
                Log.w(TAG, "Widget pin request failed for serviceId: $serviceId")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "Error requesting widget pin for serviceId: $serviceId", e)
            false
        }
    }
}
