package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import so.appio.app.data.database.DatabaseManager
import so.appio.app.data.entity.service.Service
import so.appio.app.data.entity.service.ServiceResponse
import so.appio.app.data.entity.widget.Widget
import so.appio.app.network.APIService
import so.appio.app.network.APIError
import java.util.Date

/**
 * ServiceManager handles service-related operations including fetching service data
 * with widgets and storing them in the local database.
 */
class ServiceManager(
    private val context: Context,
    private val apiService: APIService = APIService
) {

    companion object {
        private const val TAG = "LOG:ServiceManager"
    }

    private val deviceDataStore by lazy {
        val app = context.applicationContext as so.appio.app.MyApplication
        app.deviceRepository.getDeviceDataStore()
    }

    /**
     * Fetches service data with widgets from the API, stores it in the local database,
     * and creates a notification channel for the service.
     *
     * If the request is debounced (called too frequently), returns cached data from database.
     *
     * @param serviceId The ID of the service to fetch
     * @param customerUserId Customer user ID, required for new service creation, ignored for updates
     * @return Pair of Service entity and List<Widget> if found and stored successfully, null if service not found
     */
    suspend fun fetchAndStoreServiceWithWidgets(serviceId: String, customerUserId: String? = null): Pair<Service, List<Widget>>? = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Fetching and storing service with widgets for serviceId: $serviceId, customerUserId: $customerUserId")
            val serviceResponse = fetchServiceWithWidgets(serviceId)
            val (service, widgets) = storeServiceWithWidgets(serviceResponse, customerUserId)

            NotificationChannels.createChannelForService(context, service.id, service.title)

            Pair(service, widgets)
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Request debounced for serviceId: $serviceId, returning cached data")
            getCachedServiceWithWidgets(serviceId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to fetch and store service with widgets for serviceId: $serviceId", e)
            null
        }
    }

    /**
     * Fetches service data with widgets from the API only.
     *
     * @param serviceId The ID of the service to fetch
     * @return ServiceResponse from the API
     * @throws Exception if API call fails
     */
    private suspend fun fetchServiceWithWidgets(serviceId: String): ServiceResponse {
        Log.d(TAG, "Fetching service with widgets from API for serviceId: $serviceId")
        return apiService.fetchServiceWithWidgets(serviceId)
    }

    /**
     * Stores service and widgets data in the local database.
     * Uses upsert logic: insert if new, update specific fields if exists.
     * Also deletes services that are no longer linked to this device (e.g. "Docs service")
     *
     * @param serviceResponse The service response from API to store
     * @param customerUserId Customer user ID, required for new service creation, ignored for updates
     * @return Pair of Service entity and List<Widget> that were stored
     * @throws Exception if database operations fail
     */
    private suspend fun storeServiceWithWidgets(serviceResponse: ServiceResponse, customerUserId: String? = null): Pair<Service, List<Widget>> {
        Log.d(TAG, "Storing service with widgets for serviceId: ${serviceResponse.id}, customerUserId: $customerUserId")

        val serviceRepository = DatabaseManager.getServiceRepository()

        // Check if service exists
        val existingService = serviceRepository.getServiceById(serviceResponse.id)

        val service = if (existingService != null) {
            // Service exists - update only specific fields
            Log.d(TAG, "Service exists, updating fields for serviceId: ${serviceResponse.id}")
            serviceRepository.updateServiceFields(
                serviceId = serviceResponse.id,
                title = serviceResponse.title,
                description = serviceResponse.description,
                logoURL = serviceResponse.logoURL,
                bannerURL = serviceResponse.bannerURL,
                url = serviceResponse.url,
                textColor = serviceResponse.textColor,
                backgroundColor = serviceResponse.backgroundColor,
            )
            // Return updated service (preserve existing fields)
            existingService.copy(
                title = serviceResponse.title,
                description = serviceResponse.description,
                logoURL = serviceResponse.logoURL,
                bannerURL = serviceResponse.bannerURL,
                url = serviceResponse.url,
                textColor = serviceResponse.textColor,
                backgroundColor = serviceResponse.backgroundColor,
            )
        } else {
            // Service doesn't exist - insert new
            require(customerUserId != null) { "customerUserId is required for new service creation" }
            Log.d(TAG, "Service doesn't exist, inserting new serviceId: ${serviceResponse.id}")
            val newService = Service(
                id = serviceResponse.id,
                customerUserId = customerUserId,
                title = serviceResponse.title,
                description = serviceResponse.description,
                logoURL = serviceResponse.logoURL,
                bannerURL = serviceResponse.bannerURL,
                url = serviceResponse.url,
                textColor = serviceResponse.textColor,
                backgroundColor = serviceResponse.backgroundColor,
                showPreview = true,
                lastUpdate = Date(),
                lastSync = Date(),
            )
            serviceRepository.insertService(newService)
            newService
        }

        Log.d(TAG, "Service processed successfully: ${service.id}")

        // Handle widgets with upsert logic and collect stored widgets
        val storedWidgets = mutableListOf<Widget>()
        serviceResponse.widgets?.let { widgetResponses ->
            if (widgetResponses.isNotEmpty()) {
                val widgetRepository = DatabaseManager.getWidgetRepository()

                widgetResponses.forEach { widgetResponse ->
                    val existingWidget = widgetRepository.getWidgetById(widgetResponse.id)
                    val widgetConfig = WidgetImageManager.preProcessConfig(context, widgetResponse.config)

                    val storedWidget = if (existingWidget != null) {
                        // Widget exists - update only specific fields
                        Log.d(TAG, "Widget exists, updating fields for widgetId: ${widgetResponse.id}")
                        widgetRepository.updateWidgetFields(
                            widgetId = widgetResponse.id,
                            name = widgetResponse.name,
                            config = widgetConfig,
                            updatedAt = Date()
                        )
                        // Return updated widget
                        existingWidget.copy(
                            name = widgetResponse.name,
                            config = widgetConfig,
                            updatedAt = Date()
                        )
                    } else {
                        // Widget doesn't exist - insert new
                        Log.d(TAG, "Widget doesn't exist, inserting new widgetId: ${widgetResponse.id}")
                        val newWidget = Widget(
                            id = widgetResponse.id,
                            serviceId = widgetResponse.serviceId,
                            name = widgetResponse.name,
                            config = widgetConfig,
                            updatedAt = Date()
                        )
                        widgetRepository.insertWidget(newWidget)
                        newWidget
                    }
                    storedWidgets.add(storedWidget)
                }

                Log.d(TAG, "Processed ${widgetResponses.size} widgets for service: ${serviceResponse.id}")
            }
        }

        return Pair(service, storedWidgets)
    }

    /**
     * Retrieves cached service data with widgets from the local database.
     * Used when API requests are debounced to provide cached data instead of failing.
     *
     * @param serviceId The ID of the service to retrieve from cache
     * @return Pair of Service entity and List<Widget> if found in cache, null if not cached
     */
    private suspend fun getCachedServiceWithWidgets(serviceId: String): Pair<Service, List<Widget>>? {
        return try {
            val serviceRepository = DatabaseManager.getServiceRepository()
            val widgetRepository = DatabaseManager.getWidgetRepository()

            val cachedService = serviceRepository.getServiceById(serviceId)
            if (cachedService != null) {
                val cachedWidgets = widgetRepository.getWidgetsByServiceList(serviceId)
                Log.d(TAG, "Retrieved cached service with ${cachedWidgets.size} widgets for serviceId: $serviceId")
                Pair(cachedService, cachedWidgets)
            } else {
                Log.d(TAG, "No cached service found for serviceId: $serviceId")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error retrieving cached service data for serviceId: $serviceId", e)
            null
        }
    }

    /**
     * Refreshes all services and widgets data by fetching from API and updating local database.
     * This method fetches all services associated with the current device and updates the database.
     *
     * @return Boolean indicating success (true) or failure (false)
     */
    suspend fun refreshAllServices(): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            Log.d(TAG, "Starting refresh of all services")

            // Get device ID from DataStore
            val deviceId = deviceDataStore.deviceId.first()
            if (deviceId == null) {
                Log.w(TAG, "No device ID found, cannot refresh services")
                return@withContext false
            }

            // Fetch all services from API
            val servicesResponse = apiService.fetchAllServicesWithWidgets(deviceId)
            Log.d(TAG, "Fetched ${servicesResponse.size} services from API")

            // Process each service response and update database
            val successIDs = mutableListOf<String>()
            servicesResponse.forEach { serviceResponse ->
                try {
                    Log.d(TAG, "Refreshing and storing service with widgets for serviceId: ${serviceResponse.id}")
                    val (service, widgets) = storeServiceWithWidgets(serviceResponse)

                    // Create notification channel for the service
                    NotificationChannels.createChannelForService(context, service.id, service.title)

                    successIDs.add(service.id)
                    Log.d(TAG, "Successfully refreshed service: ${service.id} with ${widgets.size} widgets")
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to refresh service: ${serviceResponse.id}", e)
                }
            }

            cleanOldServices(successIDs)

            val success = successIDs.size == servicesResponse.size
            Log.d(TAG, "Refresh completed: $successIDs.size/${servicesResponse.size} services updated successfully")
            success
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Refresh request debounced, skipping")
            true // Consider debounce as success since data is recent
        } catch (e: Exception) {
            Log.e(TAG, "Failed to refresh all services", e)
            false
        }
    }

    /**
     * Cleans up old services and widgets that are no longer linked to this device.
     */
    private suspend fun cleanOldServices(serviceIds: List<String>) {
        val widgetRepository = DatabaseManager.getWidgetRepository()
        widgetRepository.deleteWidgetsForServicesNotInList(serviceIds)
        val serviceRepository = DatabaseManager.getServiceRepository()
        serviceRepository.deleteServicesNotInList(serviceIds)
    }
}
