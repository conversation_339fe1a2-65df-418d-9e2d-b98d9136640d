package so.appio.app.utils

import android.Manifest
import android.app.NotificationManager
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.compose.runtime.mutableStateOf
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import so.appio.app.data.AppDataStore
import so.appio.app.data.repository.DeviceRepository

import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
/**
 * NotificationPermissionManager handles all notification permission logic.
 *
 * This utility class encapsulates the complex notification permission flow
 * described in @Docs/notification_permissions_flow.md
 */
class NotificationPermissionManager(
    private val activity: ComponentActivity,
    private val appDataStore: AppDataStore,
    private val deviceRepository: DeviceRepository
) {

    companion object {
        private const val TAG = "LOG:NotificationPermissionManager"
    }

    private val context: Context = activity

    // Dialog state - encapsulated within this manager
    var showCustomDialog by mutableStateOf(false)
        private set

    // Permission launcher - must be initialized during activity creation
    private val requestPermissionLauncher: ActivityResultLauncher<String> =
        activity.registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
            onPermissionResult(isGranted)
        }
    

    /**
     * Requests notification permission based on current state.
     *
     * Currently handles:
     * 1. Fully enabled → Reset flow and enable notifications
     * 2. First-time users → Native dialog
     * 3. Asked before + can ask again → Native dialog
     *
     * Future phases will add support for other scenarios like
     * permanently denied, settings disabled, etc.
     */
    suspend fun request() {
        Log.d(TAG, "request() called - Android API level: ${Build.VERSION.SDK_INT}")

        // Handle Android < 13 (API < 33) - no runtime permissions, only system settings
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            val areNotificationsEnabled = NotificationManagerCompat.from(context).areNotificationsEnabled()
            Log.d(TAG, "Android < 13, notifications settings: $areNotificationsEnabled")

            // Update notification status in DeviceDataStore
            updateNotificationStatusInDataStore()

            if (areNotificationsEnabled) {
                Log.d(TAG, "Notifications enabled in settings - triggering FCM token refresh")
                FirebaseMessagingService.refreshToken(context)
            } else {
                Log.d(TAG, "Notifications disabled in settings - showing custom dialog")
                showCustomDialog = true
            }

            return
        }

        val isPermissionGranted = isPermissionGranted()
        val isNotificationsEnabled = isNotificationsEnabled()
        Log.d(TAG, "Permission check - isPermissionGranted: $isPermissionGranted, isNotificationsEnabled: $isNotificationsEnabled")

        if (isPermissionGranted && isNotificationsEnabled) {
            // Scenario 1: Fully enabled
            Log.d(TAG, "Notifications fully enabled")
            updateNotificationStatusInDataStore()
            FirebaseMessagingService.refreshToken(context)
            return
        }

        if (isPermissionGranted) {
            // Scenario 5: Permission granted but notifications disabled in settings
            // (We know isNotificationsEnabled = false here, otherwise we'd be in Scenario 1)
            Log.d(TAG, "Permission granted but notifications disabled in settings - will handle in future phases")
            updateNotificationStatusInDataStore()
            showCustomDialog = true
            return
        }

        // Check if we've asked before
        when {
            !hasRequestedBefore() -> {
                // Scenario 2: First time (never asked)
                Log.d(TAG, "First time requesting permission - showing native dialog")
                markPermissionAsRequested()
                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
            activity.shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS) -> {
                // Scenario 3: Asked before + can ask again
                Log.d(TAG, "Permission denied before but can ask again - showing native dialog")
                requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
            }
            else -> {
                // Scenario 4: Asked before + permanently denied
                Log.d(TAG, "Permission permanently denied - will handle in future phases")
                showCustomDialog = true
            }
        }
    }

    private suspend fun hasRequestedBefore() : Boolean {
        return try {
            appDataStore.isNotificationPermissionRequested.first()
        } catch (_: Exception) {
            false
        }
    }

    private suspend fun markPermissionAsRequested() {
        try {
            appDataStore.markNotificationPermissionAsRequested()
            Log.d(TAG, "Marked notification permission as requested")
        } catch (e: Exception) {
            Log.e(TAG, "Error marking permission as requested", e)
        }
    }

    private fun resetPermissionRequestedFlag() {
        try {
            // Use a coroutine scope tied to the activity lifecycle
            activity.lifecycleScope.launch {
                appDataStore.resetNotificationPermissionRequested()
                Log.d(TAG, "Reset permission requested flag - user can be asked again")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error resetting permission requested flag", e)
        }
    }

    /**
     * Handles the result of a permission request.
     *
     * @param isGranted true if permission was granted, false if denied
     */
    fun onPermissionResult(isGranted: Boolean) {
        if (isGranted) {
            Log.d(TAG, "Notification permission granted by user")
            resetPermissionRequestedFlag()
            updateNotificationStatusInDataStore()
            FirebaseMessagingService.refreshToken(context)
        } else {
            Log.d(TAG, "Notification permission denied by user, keep dialog open")
            updateNotificationStatusInDataStore()
        }
    }

    /**
     * Dismisses the custom notification dialog.
     */
    fun dismissCustomDialog() {
        showCustomDialog = false
        Log.d(TAG, "Custom notification dialog dismissed")
    }

    /**
     * Handles the "Allow" action from the custom dialog.
     * Opens app settings and dismisses the dialog.
     */
    fun onAllowCustomDialog() {
        dismissCustomDialog()
        updateNotificationStatusInDataStore()
        BrowserUtils.openAppNotificationsSettings(activity)
        Log.d(TAG, "User chose to allow notifications - opening app settings")
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun isPermissionGranted() : Boolean {
        return ContextCompat.checkSelfPermission(
            activity,
            Manifest.permission.POST_NOTIFICATIONS
        ) == PackageManager.PERMISSION_GRANTED
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun isNotificationsEnabled() : Boolean {
        return try {
            val notificationManager = activity.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.areNotificationsEnabled()
        } catch (e: Exception) {
            Log.w(TAG, "Could not check notification status", e)
            false
        }
    }

    /**
     * Checks and updates the current notification status in DeviceDataStore.
     * This should be called when the app resumes to detect changes made in settings.
     */
    fun checkAndUpdateNotificationStatus() {
        updateNotificationStatusInDataStore()
    }

    /**
     * Determines the current notification status and saves it to DeviceDataStore.
     * This combines both permission status and system notification settings.
     */
    private fun updateNotificationStatusInDataStore() {
        try {
            activity.lifecycleScope.launch {
                val isEnabled = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    // Android 13+: Check both permission and system settings
                    isPermissionGranted() && isNotificationsEnabled()
                } else {
                    // Android < 13: Only check system settings
                    NotificationManagerCompat.from(context).areNotificationsEnabled()
                }

                deviceRepository.updateNotificationEnabled(isEnabled)
                Log.d(TAG, "Updated notification status in DeviceDataStore: $isEnabled")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating notification status in DeviceDataStore", e)
        }
    }
}

