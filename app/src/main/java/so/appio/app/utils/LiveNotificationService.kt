package so.appio.app.utils

//import android.Manifest
//import android.app.Service
//import android.content.Intent
//import android.os.IBinder
//import androidx.annotation.RequiresPermission
//import androidx.core.app.NotificationCompat
//import androidx.core.app.NotificationManagerCompat
//
////TODO: live activity. also custom notifications layout might be needed for styling
//class LiveNotificationService : Service() {
//
//    companion object {
//        const val NOTIFICATION_ID = 1001 // TODO TESTING hardcoded
//        const val ACTION_START = "START_NOTIFICATION"
//        const val ACTION_UPDATE = "UPDATE_NOTIFICATION"
//        const val ACTION_STOP = "STOP_NOTIFICATION"
//        const val EXTRA_TITLE = "title" // TODO TESTING param name
//        const val EXTRA_VALUE = "value" // TODO TESTING param name
//    }
//
//    @RequiresPermission(Manifest.permission.POST_NOTIFICATIONS)
//    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
//        val action = intent?.action
//
//        val title = intent?.getStringExtra(EXTRA_TITLE) ?: "Default title"
//        val value = intent?.getStringExtra(EXTRA_VALUE) ?: "00"
//
//        when (action) {
//            ACTION_START -> {
//                startForeground(NOTIFICATION_ID, buildNotification(title, value))
//            }
//            ACTION_UPDATE -> {
//                NotificationManagerCompat.from(this).notify(NOTIFICATION_ID, buildNotification(title, value))
//            }
//            ACTION_STOP -> {
//                stopForeground(STOP_FOREGROUND_REMOVE)
//                stopSelf()
//            }
//        }
//
//        return START_STICKY
//    }
//
//    override fun onBind(intent: Intent?): IBinder? = null
//
//    private fun buildNotification(title: String, text: String) =
//        NotificationCompat.Builder(this, NotificationChannels.getDefaultNotificationChannel(this))
//            .setContentTitle(title)
//            .setContentText(text)
//            .setSmallIcon(android.R.drawable.ic_dialog_info) // TESTING Replace with your own
//            .setOngoing(true)
//            .setOnlyAlertOnce(true)
//            .build()
//}