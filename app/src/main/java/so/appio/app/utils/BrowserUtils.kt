package so.appio.app.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.util.Log
import android.widget.Toast
import androidx.core.net.toUri

/**
 * Utility functions for opening URLs in the default browser
 */
object BrowserUtils {
    private const val TAG = "LOG:BrowserUtils"
    
    /**
     * Opens a URL in the default browser
     * @param context The context to use for launching the intent
     * @param url The URL to open
     * @return true if the URL was successfully opened, false otherwise
     */
    fun openUrl(context: Context, url: String): Boolean {
        return try {
            val intent = Intent(Intent.ACTION_VIEW, url.toUri())
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
            Log.d(TAG, "Successfully opened URL: $url")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open URL: $url", e)
            false
        }
    }

    /**
     * Opens the app's notification settings
     * @param context The context to use for launching the intent
     */
    fun openAppNotificationsSettings(context: Context) {
        try {
            // Try to open notification settings directly (Android 8.0+)
            val intent = Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
            }
            context.startActivity(intent)
            Toast.makeText(context, "Enable notifications in settings", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open notification settings", e)
            // Fallback to general app settings if notification settings fail
            try {
                val fallbackIntent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                    data = Uri.fromParts("package", context.packageName, null)
                }
                context.startActivity(fallbackIntent)
                Toast.makeText(context, "Enable notifications in app settings", Toast.LENGTH_LONG).show()
            } catch (fallbackException: Exception) {
                Log.e(TAG, "Failed to open any settings", fallbackException)
                Toast.makeText(context, "Could not open settings", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * Opens the app's camera permission settings
     * @param context The context to use for launching the intent
     */
    fun openAppCameraSettings(context: Context) {
        try {
            // There is no dedicated intent for Camera settings. Open app-specific settings where user can manage camera permission
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
            Toast.makeText(context, "Enable camera permission in app settings", Toast.LENGTH_LONG).show()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open camera settings", e)
            Toast.makeText(context, "Could not open settings", Toast.LENGTH_SHORT).show()
        }
    }

}
