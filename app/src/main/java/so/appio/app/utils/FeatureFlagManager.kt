package so.appio.app.utils

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.first
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import so.appio.app.data.FeatureFlagDataStore
import so.appio.app.network.APIError
import so.appio.app.network.APIService

/**
 * FeatureFlagManager handles fetching and storing feature flags from the API.
 * 
 * This manager is responsible for:
 * - Fetching feature flags from the API using APIService.fetchFeatureFlags()
 * - Storing the fetched data in FeatureFlagDataStore
 * - Handling errors gracefully with proper logging
 */
class FeatureFlagManager(
    context: Context
) {

    companion object {
        private const val TAG = "LOG:FeatureFlagManager"
    }

    private val featureFlagDataStore = FeatureFlagDataStore(context)

    /**
     * Check for feature flags by fetching from API and storing in DataStore
     */
    suspend fun check() = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting feature flags check")

            val featureFlagsEntity = APIService.fetchFeatureFlags()
            if (featureFlagsEntity == null) {
                Log.d(TAG, "No feature flags received from API")
                return@withContext
            }

            if (featureFlagsEntity.id.isEmpty()) {
                Log.d(TAG, "Feature flags response has empty fields")
                return@withContext
            }

            // Store feature flags in DataStore
            featureFlagDataStore.updateFeatureFlag(
                id = featureFlagsEntity.id,
                version = featureFlagsEntity.version,
                config = featureFlagsEntity.config
            )

            Log.d(TAG, "Feature flags stored successfully. ID: ${featureFlagsEntity.id}, Version: ${featureFlagsEntity.version}")
        } catch (_: APIError.Debounce) {
            Log.w(TAG, "Feature flags request debounced, skipping")
        } catch (e: Exception) {
            Log.e(TAG, "Error during feature flags check", e)
        }
    }

    /**
     * Get a feature flag value by key from the stored configuration.
     *
     * @param key The key to look up in the feature flag configuration
     * @return The value of type T if found, null otherwise
     */
    internal suspend inline fun <reified T> get(key: String): T? = withContext(Dispatchers.IO) {
        try {
            val featureFlagData = featureFlagDataStore.featureFlag.first() ?: return@withContext null
            val configJson = Json.parseToJsonElement(featureFlagData.config).jsonObject
            val jsonElement = configJson[key]

            if (jsonElement == null) {
                Log.w(TAG, "Feature Flag config key: $key was not found. id: ${featureFlagData.id}, version: ${featureFlagData.version}, config: ${featureFlagData.config}")
                return@withContext null
            }

            return@withContext Json.decodeFromJsonElement<T>(jsonElement)
        } catch (e: Exception) {
            Log.w(TAG, "Failed decoding Feature Flag config into Json or parsing key: $key", e)
            return@withContext null
        }
    }
}
