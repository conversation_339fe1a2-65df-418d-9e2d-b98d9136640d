package so.appio.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.LruCache
import android.util.Log
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.security.MessageDigest
import java.util.concurrent.TimeUnit

/**
 * Manages persistent caching of images to avoid network requests during usage.
 * Images are stored persistently in the app's cache directory and survive app restarts.
 * Also includes in-memory cache for instant access during scrolling.
 */
object ImageCacheManager {
    private const val TAG = "LOG:ImageCacheManager"
    private const val CACHE_DIRECTORY = "image_cache"

    // Application context for operations
    private var appContext: Context? = null

    // In-memory cache for instant access (LRU cache with max 20 images for your ≤10 services)
    private val memoryCache = LruCache<String, Bitmap>(20)

    /**
     * Initialize the ImageCacheManager with application context
     * This should be called once during app startup
     */
    fun initialize(context: Context) {
        appContext = context.applicationContext
        Log.d(TAG, "ImageCacheManager initialized successfully")
    }

    /**
     * Safely get the application context, logging a warning if not initialized
     * @return The application context, or null if not initialized
     */
    private fun getContextSafely(): Context? {
        return appContext ?: run {
            Log.w(TAG, "ImageCacheManager not initialized. Call initialize() first.")
            null
        }
    }

    /**
     * Pre-cache an image from the given URL to persistent storage without returning anything
     * @param url The URL of the image to cache persistently
     * @param force If true, re-download even if cached; if false, skip if already cached
     */
    suspend fun preCacheImage(url: String, force: Boolean = false) {
        getCachedImage(url, force)
    }

    /**
     * Get a cached image from the given URL, downloading and storing persistently if needed
     * @param url The URL of the image to cache
     * @param force If true, re-download even if cached; if false, return cached version if available
     * @return The cached or downloaded bitmap, or a placeholder if download fails
     */
    suspend fun getCachedImage(url: String, force: Boolean = false): Bitmap? {
        return withContext(Dispatchers.IO) {
            val context = getContextSafely()
            if (context == null) {
                Log.w(TAG, "Cannot cache image from URL $url - ImageCacheManager not initialized")
                return@withContext null
            }

            val hashedFilename = generateHashedFilename(url)

            if (!force) {
                val bitmap = getPreCachedImage(hashedFilename)
                if (bitmap != null) {
                    return@withContext bitmap
                }
            }

            return@withContext downloadAndStoreImage(url, hashedFilename, context)
        }
    }

    /**
     * Get a pre-cached image from the given URL, without downloading if not cached
     * @param filename Of cached image
     * @return The cached bitmap, or null if not cached
     */
    fun getPreCachedImage(filename: String): Bitmap? {
        val context = getContextSafely()
        if (context == null) {
            Log.w(TAG, "Cannot cache image from filename $filename - ImageCacheManager not initialized")
            return null
        }

        try {
            // In-memory cache
            val cachedBitmap = memoryCache.get(filename)
            if (cachedBitmap != null) {
                Log.d(TAG, "Loading image from memory: $filename")
                return cachedBitmap
            }

            // File cache
            val cachedFile = getCachedImageFile(context, filename)
            if (cachedFile.exists()) {
                Log.d(TAG, "Loading image from file: $filename")
                val bitmap = BitmapFactory.decodeFile(cachedFile.absolutePath)
                if (bitmap != null) {
                    // Store in memory cache for next time
                    memoryCache.put(filename, bitmap)
                    Log.d(TAG, "Successfully loaded image from file: $filename")
                    return bitmap
                } else {
                    Log.w(TAG, "File exists but couldn't be decoded into bitmap: $filename")
                    cachedFile.delete()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to load pre-cached image: $filename: ${e.message}", e)
            return null
        }

        return null
    }

    /**
     * Download and store an image from the given URL
     * @param url The URL of the image to download
     * @param hashedFilename The hashed filename to store the image as
     * @param context The application context
     * @return The downloaded bitmap, or null if download fails
     */
    private fun downloadAndStoreImage(url: String, hashedFilename: String, context: Context): Bitmap? {
        try {
            // Check network connectivity
            if (!NetworkUtils.isNetworkAvailable(context)) {
                Log.w(TAG, "No network connection available for downloading image from URL: $url")
                return null
            }

            // Download image
            Log.d(TAG, "Downloading image from URL: $url")
            val requestOptions = RequestOptions()
                .diskCacheStrategy(DiskCacheStrategy.DATA)
                .timeout(15000) // 15 second timeout

            val bitmap = Glide.with(context)
                .asBitmap() // Validating that bitmap is a valid image
                .load(url)
                .apply(requestOptions)
                .submit()
                .get(15, TimeUnit.SECONDS)

            if (bitmap != null) {
                // Store to file
                saveBitmapToCache(context, bitmap, hashedFilename)
                // Store in-memory
                memoryCache.put(hashedFilename, bitmap)
                Log.d(TAG, "Successfully downloaded and cached image: $url")
                return bitmap
            } else {
                Log.e(TAG, "Downloaded image is empty: $url")
                return null
            }

        } catch (e: Exception) {
            Log.e(TAG, "Failed to download and cache image: $url: ${e.message}", e)
            return null
        }
    }

    /**
     * Generate a consistent filename from URL using MD5 hash
     */
    fun generateHashedFilename(url: String): String {
        val md5 = MessageDigest.getInstance("MD5")
        val hashBytes = md5.digest(url.toByteArray())
        val hashString = hashBytes.joinToString("") { "%02x".format(it) }
        return "$hashString.png"
    }

    /**
     * Get the dedicated directory for persistent image storage
     * Images are stored in: /data/data/so.appio.app/cache/image_cache/
     * This directory persists until app uninstall (not cleared by system or cache clearing)
     */
    private fun getImageCacheDir(context: Context): File {
        val cacheDir = File(context.cacheDir, CACHE_DIRECTORY)
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }
        return cacheDir
    }

    /**
     * Get the cached image file for a given hashed filename
     */
    fun getCachedImageFile(context: Context, hashedFilename: String): File {
        return File(getImageCacheDir(context), hashedFilename)
    }

    /**
     * Save bitmap to cache with the given filename
     */
    private fun saveBitmapToCache(context: Context, bitmap: Bitmap, hashedFilename: String) {
        try {
            val cacheFile = getCachedImageFile(context, hashedFilename)
            FileOutputStream(cacheFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
            }
            Log.d(TAG, "Bitmap saved to cache: ${cacheFile.absolutePath}")
        } catch (e: IOException) {
            Log.e(TAG, "Failed to save bitmap to cache: ${e.message}", e)
        }
    }
}
