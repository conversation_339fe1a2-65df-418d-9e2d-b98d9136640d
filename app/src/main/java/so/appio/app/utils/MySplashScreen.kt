package so.appio.app.utils

import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.AnticipateInterpolator
import androidx.core.animation.doOnEnd
import so.appio.app.ui.screens.MainViewModel
import androidx.core.splashscreen.SplashScreen

object MySplashScreen {
    private const val TAG = "LOG:MySplashScreen"

    fun initialize(splashScreen: SplashScreen, viewModel: MainViewModel) {

        // Tell the splash screen when to go away
        splashScreen.setKeepOnScreenCondition {
            viewModel.isSplashScreenLoading.value
        }

        // Fade away the splash screen
        splashScreen.setOnExitAnimationListener { splashScreenView ->
            val fadeOut = ObjectAnimator.ofFloat(
                splashScreenView.view, View.ALPHA, 1f, 0f
            ).apply {
                duration = 300L
                interpolator = AnticipateInterpolator()
                doOnEnd {
                    splashScreenView.remove()
                }
            }

            fadeOut.start()
        }
    }
}