package so.appio.app.utils

import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean

/**
 * <PERSON><PERSON> to track service registration state across the application.
 * Used to prevent race conditions between new service registration and service refresh operations.
 */
object ServiceRegistrationState {
    
    private const val TAG = "LOG:ServiceRegistrationState"
    
    // Use AtomicBoolean for thread-safe operations
    private val isRegistrationInProgress = AtomicBoolean(false)
    
    /**
     * Set the service registration state
     * @param inProgress true if service registration is in progress, false otherwise
     */
    fun setRegistrationInProgress(inProgress: Boolean) {
        val previousState = isRegistrationInProgress.getAndSet(inProgress)
        if (previousState != inProgress) {
            Log.d(TAG, "Service registration state changed: $previousState -> $inProgress")
        }
    }
    
    /**
     * Check if service registration is currently in progress
     * @return true if service registration is in progress, false otherwise
     */
    fun isRegistrationInProgress(): Bo<PERSON>an {
        return isRegistrationInProgress.get()
    }
    
    /**
     * Execute a block of code while marking service registration as in progress
     * This ensures the state is properly reset even if an exception occurs
     * @param block the code block to execute
     * @return the result of the block execution
     */
    suspend fun <T> withRegistrationInProgress(block: suspend () -> T): T {
        setRegistrationInProgress(true)
        return try {
            block()
        } finally {
            setRegistrationInProgress(false)
        }
    }
}
