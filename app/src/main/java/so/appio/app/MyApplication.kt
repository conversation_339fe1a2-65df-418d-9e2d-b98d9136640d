package so.appio.app

import android.app.Application
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import so.appio.app.data.database.DatabaseManager
import so.appio.app.network.APIClient
import so.appio.app.data.repository.DeviceRepository
import so.appio.app.utils.DeviceManager
import so.appio.app.utils.GooglePlayServices
import so.appio.app.utils.ImageCacheManager
import so.appio.app.utils.NotificationChannels
import so.appio.app.utils.ServiceRefreshWorker
import so.appio.app.utils.SentryErrorHandler
import io.sentry.Sentry
import io.sentry.android.core.SentryAndroid

class MyApplication : Application() {

    companion object {
        internal const val TAG = "LOG:MyApplication"
        internal val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    }

    // Single instances for dependency injection
    lateinit var deviceRepository: DeviceRepository
        private set
    lateinit var deviceManager: DeviceManager
        private set

    override fun onCreate() {
        super.onCreate()
        Log.d(TAG, "onCreate called")

        configureSentry()

        // Initialize core services
        GooglePlayServices.initializeFirebase(this)
        ImageCacheManager.initialize(this)
        DatabaseManager.initialize(this)
        APIClient.initialize(this)
        NotificationChannels.createDefaultNotificationChannel(this)

        // Initialize device-related components
        deviceRepository = DeviceRepository(this)
        deviceRepository.initialize()
        deviceManager = DeviceManager(this)

        // Do async operations
        createServiceNotificationChannels()
        checkDeviceSync()

        // Start periodic service refresh
        ServiceRefreshWorker.startAutoRefresh(this)
    }

    /**
     * Creates notification channels for all stored Service entities.
     * This is called after DatabaseManager initialization to ensure database access is available.
     */
    private fun createServiceNotificationChannels() {
        applicationScope.launch {
            try {
                Log.d(TAG, "Creating notification channels for stored services")

                val serviceRepository = DatabaseManager.getServiceRepository()
                val services = serviceRepository.getAllServicesList()

                services.forEach { service ->
                    NotificationChannels.createChannelForService(
                        context = this@MyApplication,
                        serviceId = service.id,
                        serviceTitle = service.title
                    )
                    Log.d(TAG, "Created notification channel for service: ${service.title} (${service.id})")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error creating service notification channels", e)
            }
        }
    }

    /**
     * Check if device data is out of sync and sync if needed.
     * This runs asynchronously after DeviceRepository is initialized.
     */
    private fun checkDeviceSync() {
        applicationScope.launch {
            try {
                Log.d(TAG, "Checking device sync status")
                deviceRepository.checkAndSyncIfNeeded()
                Log.d(TAG, "Device sync check completed")
            } catch (e: Exception) {
                Log.e(TAG, "Error checking device sync", e)
            }
        }
    }

    /**
     * Refresh all services data when app comes to foreground.
     * This ensures data stays fresh when user returns to the app.
     */
    fun refreshServices() {
        Log.d(TAG, "Triggering service refresh on app resume")
        ServiceRefreshWorker.refreshServicesForEvent(this, ServiceRefreshWorker.REASON_APP_RESUME)
    }

    /**
     * Configure Sentry monitoring for crash reporting and error tracking.
     * Manual initialization with comprehensive filtering control.
     */
    private fun configureSentry() {
        try {
            Log.d(TAG, "Initializing Sentry with custom filtering")

            if (BuildConfig.SENTRY_DSN.isEmpty()) {
                Log.w(TAG, "Sentry DSN is empty, skipping initialization")
                return
            }

            // Manual initialization
            SentryAndroid.init(this) { options ->
                // Basic configuration - DSN from local.properties
                options.dsn = BuildConfig.SENTRY_DSN
                options.isDebug = BuildConfig.DEBUG
                options.environment = if (BuildConfig.DEBUG) "debug" else "release"
                options.release = BuildConfig.VERSION_NAME

                // Performance monitoring
                options.tracesSampleRate = 0.1

                // Session replay
                options.sessionReplay.onErrorSampleRate = 1.0
                options.sessionReplay.sessionSampleRate = 0.1

                // Additional features
                options.isSendDefaultPii = true
                options.isAttachScreenshot = true
                options.isAttachViewHierarchy = true
                options.isEnableUserInteractionTracing = true

                // Disable automatic integrations that bypass setBeforeSend
                options.isEnableAutoSessionTracking = false
                options.isEnableUncaughtExceptionHandler = false // We use our own
                options.isEnableAppLifecycleBreadcrumbs = false

                // Add custom filtering here if needed:
                options.setBeforeSend { event, hint ->
                    event.setTag("custom.filter", "on")

                    // Filter out getSplashScreen() NoSuchMethodError exceptions
                    // Check multiple sources where the exception might be stored
                    val throwable = event.throwable
                    val exceptions = event.exceptions

                    // Check direct throwable
                    if (throwable is NoSuchMethodError &&
                        throwable.message?.contains("getSplashScreen()") == true) {
                        Log.d(TAG, "Filtering out getSplashScreen() NoSuchMethodError from Sentry (direct throwable)")
                        return@setBeforeSend null
                    }

                    // Check exceptions list
                    exceptions?.forEach { sentryException ->
                        if (sentryException.type == "NoSuchMethodError" &&
                            sentryException.value?.contains("getSplashScreen()") == true) {
                            Log.d(TAG, "Filtering out getSplashScreen() NoSuchMethodError from Sentry (exceptions list)")
                            return@setBeforeSend null
                        }
                    }

                    // Check event message
                    if (event.message?.message?.contains("getSplashScreen()") == true) {
                        Log.d(TAG, "Filtering out getSplashScreen() NoSuchMethodError from Sentry (event message)")
                        return@setBeforeSend null
                    }

                    event
                }

                Log.d(TAG, "Sentry options configured")
            }

            // Install global exception handler for uncaught exceptions
            SentryErrorHandler.installGlobalExceptionHandler()

            // Add additional context
            Sentry.configureScope { scope ->
                scope.setTag("app.component", "MyApplication2")
                scope.setTag("build.type", if (BuildConfig.DEBUG) "debug" else "release")

                // Add custom context, showed in UI as "Additional Data"
                scope.setExtra("app.version", BuildConfig.VERSION_NAME)
                scope.setExtra("app.version.code", BuildConfig.VERSION_CODE.toString())
                scope.setExtra("initialization.timestamp", System.currentTimeMillis().toString())
            }

            Log.d(TAG, "Sentry initialization completed")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure Sentry", e)
        }
    }
}