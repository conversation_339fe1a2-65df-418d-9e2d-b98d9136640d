package so.appio.app.network

import okhttp3.Dns
import okhttp3.OkHttpClient
import java.net.Inet4Address
import java.net.InetAddress
import java.util.concurrent.TimeUnit

/**
 * Shared HTTP client configuration for consistent network behavior across the app.
 * Used by both APIClient and Glide's AppGlideModule.
 */
object HttpClientConfig {

    /**
     * Custom DNS resolver that prefers IPv4 addresses to avoid IPv6 connectivity issues.
     * This resolves the slow API calls caused by IPv6 connection timeouts.
     */
    private val ipv4PreferredDns = object : Dns {
        override fun lookup(hostname: String): List<InetAddress> {
            val addresses = Dns.SYSTEM.lookup(hostname)
            // Separate IPv4 and IPv6 addresses
            val ipv4Addresses = addresses.filter { it is Inet4Address }
            val ipv6Addresses = addresses.filter { it !is Inet4Address }

            // Return IPv4 addresses first, then IPv6 as fallback
            return ipv4Addresses + ipv6Addresses
        }
    }
    
    /**
     * Creates a configured OkHttpClient with standard timeouts and settings.
     * This configuration is shared between API calls and image loading.
     */
    fun createClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .dns(ipv4PreferredDns)
            .callTimeout(30, TimeUnit.SECONDS)  // Total time for entire request
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .writeTimeout(10, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .followRedirects(true)
            .followSslRedirects(true)
            .build()
    }
}
