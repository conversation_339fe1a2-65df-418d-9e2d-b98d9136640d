package so.appio.app.network

import android.content.Context
import android.os.Build
import android.util.Log
import com.google.firebase.installations.FirebaseInstallations
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.tasks.await
import kotlinx.serialization.json.Json
import kotlinx.serialization.SerializationException
import okhttp3.*
import okhttp3.HttpUrl.Companion.toHttpUrl
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import so.appio.app.BuildConfig
import so.appio.app.utils.SentryErrorHandler

object APIClient {
    const val TAG = "LOG:APIClient"
    const val BASE_URL = "https://api.appio.so/mobile"

    @JvmStatic
    var appContext: Context? = null
        private set

    @JvmStatic
    val httpClient: OkHttpClient by lazy { HttpClientConfig.createClient() }

    // JSON configuration that ignores unmapped fields to prevent FailedToDecode errors
    val json = Json {
        ignoreUnknownKeys = true
    }

    fun initialize(context: Context) {
        appContext = context.applicationContext
    }

    /**
     * Get device identifier using Firebase Installations ID
     * Falls back to empty string if Firebase is not available
     */
    suspend fun getDeviceIdentifier(): String {
        return try {
            FirebaseInstallations.getInstance().id.await()
        } catch (e: Exception) {
            Log.w(TAG, "Failed to get Firebase Installation ID: ${e.message}")
            ""
        }
    }

    suspend inline fun <reified T> performRequest(
        endpoint: String,
        method: String,
        headers: Map<String, String> = emptyMap(),
        body: String? = null,
        expectedStatusCodes: List<Int> = listOf(200),
    ): T? {
        val context = appContext
            ?: throw IllegalStateException("APIClient not initialized. Call initialize() first.")

        val httpUrl = "$BASE_URL$endpoint".toHttpUrl()
        val url = httpUrl.toString()

        val requestBody = body?.toRequestBody("application/json".toMediaType())

        val requestBuilder = Request.Builder()
            .url(httpUrl)
            .method(method, requestBody)
            .addHeader("Authorization", "Bearer ${BuildConfig.API_AUTH_TOKEN}")
            .addHeader("Content-Type", "application/json")
            .addHeader("X-App-Platform", "android")
            .addHeader("X-App-Version", BuildConfig.VERSION_NAME)
            .addHeader("X-System-Version", Build.VERSION.RELEASE ?: "") // Android OS version. (e.g. "13")
            .addHeader("X-Device-Identifier", getDeviceIdentifier())
            .addHeader("X-Screen-Size", getScreenSize(context))

        for ((key, value) in headers) {
            requestBuilder.addHeader(key, value)
        }

        val request = requestBuilder.build()

        try {
            val response = withContext(Dispatchers.IO) { httpClient.newCall(request).execute() }
            val responseBody = response.body?.string() ?: throw APIError.BadServerResponse

            Log.d(TAG, "---------- <API>")
            Log.d(TAG, "API method: $method")
            Log.d(TAG, "API request url: $url")
            Log.d(TAG, "API request headers: ${request.headers}")
            Log.d(TAG, "API request body: $body")
            Log.d(TAG, "API response code: ${response.code}")
            Log.d(TAG, "API response body: $responseBody")
            Log.d(TAG, "---------- </API>")

            if (response.code !in expectedStatusCodes) {
                val error = APIError.UnexpectedStatusCode(response.code, responseBody)
                SentryErrorHandler.reportApiError(
                    exception = error,
                    method = method,
                    url = url,
                    statusCode = response.code,
                    responseBody = responseBody
                )
                throw error
            }

            if (response.code == 404) {
                return null
            }

            return try {
                Log.d(TAG, "Decoding API response to ${T::class.java.name}")
                json.decodeFromString(responseBody)
            } catch (e: SerializationException) {
                val error = APIError.FailedToDecode(responseBody, e)
                SentryErrorHandler.reportApiError(
                    exception = error,
                    method = method,
                    url = url,
                    statusCode = response.code,
                    responseBody = responseBody
                )
                throw error
            }
        } catch (e: java.net.SocketTimeoutException) {
            Log.e(TAG, "DNS resolution failed for $url: ${e.message}")
            throw e
//            val error = APIError.NetworkTimeout
//            SentryErrorHandler.reportApiError(
//                exception = error,
//                method = method,
//                url = url
//            )
//            throw error
        } catch (e: java.net.UnknownHostException) {
            Log.e(TAG, "DNS resolution failed for $url: ${e.message}")
            throw e
//            val error = APIError.NetworkTimeout
//            SentryErrorHandler.reportApiError(
//                exception = error,
//                method = method,
//                url = url
//            )
//            throw error
        } catch (e: APIError) {
            // APIError exceptions are already handled above, just re-throw
            Log.e(TAG, "API error: ${e.message}")
            throw e
        } catch (e: CancellationException) {
            // Don't reportCoroutine cancellation to Sentry
            Log.d(TAG, "API request cancelled: $method $url")
            throw e
        } catch (e: Exception) {
            val error = APIError.NetworkError(e)
            SentryErrorHandler.reportApiError(
                exception = error,
                method = method,
                url = url
            )
            throw error
        }
    }

    fun getScreenSize(context: Context): String {
        val metrics = context.resources.displayMetrics
        return "${metrics.widthPixels}x${metrics.heightPixels}"
    }
}
