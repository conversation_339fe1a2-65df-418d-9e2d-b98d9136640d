package so.appio.app.network

/**
 * Sealed class hierarchy for API-related errors.
 *
 * This class extends Exception for compatibility with existing error handling,
 * but does not implement Serializable to avoid serialization complexities.
 * If serialization is needed, convert to a simple error code/message pair.
 */
sealed class APIError(message: String) : Exception(message) {
    object BadServerResponse : APIError("Bad server response.")
    object NetworkTimeout : APIError("Network timeout.")
    data class NetworkError(val error: Throwable) : APIError("Network error: ${error.localizedMessage}")
    data class UnexpectedStatusCode(val code: Int, val responseBody: String) : APIError("Unexpected status code $code: $responseBody")
    data class FailedToDecode(val data: String, val error: Throwable) : APIError("Failed to decode response: $data - ${error.localizedMessage}")
    object Debounce : APIError("Request debounced.")
}
