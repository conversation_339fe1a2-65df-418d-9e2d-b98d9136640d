package so.appio.app.widgets

import android.appwidget.AppWidgetManager
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch
import so.appio.app.ui.theme.AppioAppTheme
import android.content.Intent
import androidx.activity.enableEdgeToEdge
import androidx.glance.appwidget.GlanceAppWidgetManager
import androidx.glance.appwidget.state.updateAppWidgetState
import so.appio.app.ui.widgets.WidgetConfigScreen
import so.appio.app.ui.widgets.WidgetConfigData
import so.appio.app.ui.widgets.WidgetConfigResult
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.glance.appwidget.state.getAppWidgetState
import androidx.glance.state.PreferencesGlanceStateDefinition
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import so.appio.app.data.database.DatabaseManager
import androidx.activity.OnBackPressedCallback

class AppioWidgetConfigActivity : ComponentActivity() {

    companion object {
        private const val TAG = "LOG:AppioWidgetConfigActivity"
    }

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Find the widget id from the intent
        val appWidgetId = intent?.extras?.getInt(
            AppWidgetManager.EXTRA_APPWIDGET_ID,
            AppWidgetManager.INVALID_APPWIDGET_ID
        ) ?: AppWidgetManager.INVALID_APPWIDGET_ID

        // If this activity was started with an intent without an app widget ID, finish with an error.
        if (appWidgetId == AppWidgetManager.INVALID_APPWIDGET_ID) {
            Log.e(TAG, "Invalid app widget ID")
            finish()
            return
        }

        // No need to get preselected service ID from intent - it's stored in widget state
        Log.d(TAG, "Configuring widget with ID: $appWidgetId")

        // If the user backs out of the activity before reaching the end, the system notifies the
        // app widget host that the configuration is canceled and the host doesn't add the widget
        val resultValue = Intent().putExtra(AppWidgetManager.EXTRA_APPWIDGET_ID, appWidgetId)
        setResult(RESULT_CANCELED, resultValue)

        // Handle back button press using modern OnBackPressedDispatcher
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // When user backs out, ensure the widget is not created
                Log.d(TAG, "User backed out of widget configuration")
                setResult(RESULT_CANCELED)
                finishAndRemoveTask()
            }
        })

        setContent {
            AppioAppTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    WidgetConfigScreenWithSavedValues(
                        appWidgetId = appWidgetId,
                        onConfigurationResult = { result ->
                            handleConfigurationResult(appWidgetId, result)
                        }
                    )
                }
            }
        }
    }

    private fun handleConfigurationResult(appWidgetId: Int, result: WidgetConfigResult) {
        when (result) {
            is WidgetConfigResult.Success -> {
                saveConfiguration(appWidgetId, result.configData)
            }
            is WidgetConfigResult.Cancelled -> {
                // User cancelled or no services available - don't create widget
                Log.d(TAG, "Widget configuration cancelled")
                setResult(RESULT_CANCELED)
                finishAndRemoveTask()
            }
        }
    }

    private fun saveConfiguration(appWidgetId: Int, configData: WidgetConfigData) {
        lifecycleScope.launch {
            try {
                val context = this@AppioWidgetConfigActivity
                val glanceManager = GlanceAppWidgetManager(context)
                val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                if (configData.serviceId.isBlank() || configData.widgetId.isBlank() || configData.widgetConfig.isBlank()) {
                    Log.e(TAG, "Invalid widget configuration data - serviceId: ${configData.serviceId}, widgetId: ${configData.widgetId}, configLength: ${configData.widgetConfig.length}")
                    return@launch
                }

                Log.d(TAG, "Saving widget configuration - serviceId: ${configData.serviceId}, widgetId: ${configData.widgetId}")

                // Save the widget configuration via glance DataStore
                updateAppWidgetState(context, glanceId) { prefs ->
                    prefs[AppioWidget.KEY_SERVICE_ID] = configData.serviceId
                    prefs[AppioWidget.KEY_WIDGET_ID] = configData.widgetId
                    prefs[AppioWidget.KEY_WIDGET_CONFIG] = configData.widgetConfig
                }

                // IMMEDIATE DATA FETCH: Trigger immediate data fetch for this new widget
                AppioWidgetUpdateWorker.updateWidgetsForEvent(
                    context,
                    AppioWidgetUpdateWorker.REASON_CONFIG_SAVED
                )

                // Update single widget
                AppioWidget().update(context, glanceId)
                Log.d(TAG, "Called update for widget: $glanceId")

//                // Update all Glance widgets to reflect the configuration change
//                AppioWidget().updateAll(context)
//                Log.d(TAG, "Called updateAll() for all widgets")

                val resultValue = Intent().putExtra(
                    AppWidgetManager.EXTRA_APPWIDGET_ID,
                    appWidgetId,
                )
                setResult(RESULT_OK, resultValue)

                // Finish the activity and ensure it doesn't interfere with the home screen
                finishAndRemoveTask()
                Log.d(TAG, "Widget configuration completed and activity finished")
            } catch (e: Exception) {
                Log.e(TAG, "Error configuring widget", e)
                setResult(RESULT_CANCELED)
                finishAndRemoveTask()
            }
        }
    }

    @Composable
    private fun WidgetConfigScreenWithSavedValues(
        appWidgetId: Int,
        onConfigurationResult: (WidgetConfigResult) -> Unit
    ) {
        var initialServiceId by remember { mutableStateOf<String?>(null) }
        var initialWidgetId by remember { mutableStateOf<String?>(null) }
        var isLoadingConfig by remember { mutableStateOf(true) }

        // Read saved values from widget state and options
        LaunchedEffect(appWidgetId) {
            try {
                val glanceManager = GlanceAppWidgetManager(this@AppioWidgetConfigActivity)
                val glanceId = glanceManager.getGlanceIdBy(appWidgetId)

                // Read widget state using the same approach as the widget
                val state = getAppWidgetState(this@AppioWidgetConfigActivity, PreferencesGlanceStateDefinition, glanceId)
                val savedServiceId = state[AppioWidget.KEY_SERVICE_ID]
                val savedWidgetId = state[AppioWidget.KEY_WIDGET_ID]

                initialServiceId = validateService(savedServiceId)
                initialWidgetId = validateWidget(savedWidgetId)

                Log.d(TAG, "Read saved values - serviceId: $savedServiceId, widgetId: $savedWidgetId")
                Log.d(TAG, "Using initial values - serviceId: $initialServiceId, widgetId: $initialWidgetId")
            } catch (e: Exception) {
                Log.w(TAG, "Could not read saved values: ${e.message}")
                // No fallback needed - widget state is the source of truth
                initialServiceId = null
            } finally {
                isLoadingConfig = false
            }
        }

        if (!isLoadingConfig) {
            WidgetConfigScreen(
                onConfigurationResult = onConfigurationResult,
                initialServiceId = initialServiceId,
                initialWidgetId = initialWidgetId
            )
        }
        // Show nothing while loading config (very brief)
    }

    /**
     * Validates that a service ID exists in the database
     */
    private suspend fun validateService(serviceId: String?): String? {
        return serviceId?.takeIf { DatabaseManager.getServiceRepository().serviceExists(it) }
    }

    /**
     * Validates that a widget ID exists in the database
     */
    private suspend fun validateWidget(widgetId: String?): String? {
        return widgetId?.takeIf { DatabaseManager.getWidgetRepository().widgetExists(it) }
    }
}


