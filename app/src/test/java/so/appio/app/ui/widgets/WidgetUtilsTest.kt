package so.appio.app.ui.widgets

import androidx.glance.text.TextAlign
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.Test
import org.junit.Assert.*

class WidgetUtilsTest {

    @Test
    fun `convertTextAlign should return correct TextAlign for valid strings`() {
        assertEquals(TextAlign.Center, convertTextAlign("center"))
        assertEquals(TextAlign.Start, convertTextAlign("start"))
        assertEquals(TextAlign.End, convertTextAlign("end"))
        assertEquals(TextAlign.Left, convertTextAlign("left"))
        assertEquals(TextAlign.Right, convertTextAlign("right"))
    }

    @Test
    fun `convertTextAlign should be case insensitive`() {
        assertEquals(TextAlign.Center, convertTextAlign("CENTER"))
        assertEquals(TextAlign.Start, convertTextAlign("START"))
        assertEquals(TextAlign.End, convertTextAlign("END"))
        assertEquals(TextAlign.Left, convertTextAlign("LEFT"))
        assertEquals(TextAlign.Right, convertTextAlign("RIGHT"))
    }

    @Test
    fun `convertTextAlign should return Start for null input`() {
        assertEquals(TextAlign.Start, convertTextAlign(null))
    }

    @Test
    fun `convertTextAlign should return Start for invalid input`() {
        assertEquals(TextAlign.Start, convertTextAlign("invalid"))
        assertEquals(TextAlign.Start, convertTextAlign(""))
        assertEquals(TextAlign.Start, convertTextAlign("unknown"))
    }

    @Test
    fun `convertColor should return non-zero for hex color`() {
        val result = convertColor("#FF0000")
        println("convertColor('#FF0000') = $result")
        assertNotEquals("convertColor('#FF0000') should not return 0", 0, result)
    }

    @Test
    fun `convertColor should return BLUE for primary`() {
        val result = convertColor("primary")
        println("convertColor('primary') = $result")
        assertEquals("convertColor('primary') should return BLUE", android.graphics.Color.BLUE, result)
    }

    @Test
    fun `convertColor should return non-zero for purple`() {
        val result = convertColor("purple")
        println("convertColor('purple') = $result")
        assertNotEquals("convertColor('purple') should not return 0", 0, result)
        // Should return either Android's default purple or our custom purple (0xFFAF52DE)
        assertTrue("convertColor('purple') should return a valid purple color",
            result == 0xFFAF52DE.toInt() || result != 0)
    }

    @Test
    fun `parseRefreshButtonProperties should parse style property correctly`() {
        // Test with style property
        val jsonWithStyle = JsonObject(mapOf(
            "text" to JsonPrimitive("Refresh"),
            "style" to JsonPrimitive("icon-primary")
        ))
        val propertiesWithStyle = parseRefreshButtonProperties(jsonWithStyle)
        assertNotNull(propertiesWithStyle)
        assertEquals("Refresh", propertiesWithStyle?.text)
        assertEquals("icon-primary", propertiesWithStyle?.style)
    }

    @Test
    fun `parseRefreshButtonProperties should handle missing style property`() {
        // Test without style property
        val jsonWithoutStyle = JsonObject(mapOf(
            "text" to JsonPrimitive("Refresh")
        ))
        val propertiesWithoutStyle = parseRefreshButtonProperties(jsonWithoutStyle)
        assertNotNull(propertiesWithoutStyle)
        assertEquals("Refresh", propertiesWithoutStyle?.text)
        assertNull(propertiesWithoutStyle?.style)
    }

    @Test
    fun `parseRefreshButtonProperties should parse all style values`() {
        val styleValues = listOf("filled", "outline", "icon-primary", "icon-secondary", "icon-only")

        styleValues.forEach { style ->
            val json = JsonObject(mapOf(
                "style" to JsonPrimitive(style)
            ))
            val properties = parseRefreshButtonProperties(json)
            assertNotNull("Properties should not be null for style: $style", properties)
            assertEquals("Style should match for: $style", style, properties?.style)
        }
    }

    @Test
    fun `parseRefreshButtonProperties should parse tint property correctly`() {
        // Test with tint property
        val jsonWithTint = JsonObject(mapOf(
            "text" to JsonPrimitive("Refresh"),
            "tint" to JsonPrimitive("#FF0000"),
            "style" to JsonPrimitive("filled")
        ))
        val propertiesWithTint = parseRefreshButtonProperties(jsonWithTint)
        assertNotNull(propertiesWithTint)
        assertEquals("Refresh", propertiesWithTint?.text)
        assertEquals("#FF0000", propertiesWithTint?.tint)
        assertEquals("filled", propertiesWithTint?.style)
    }

    @Test
    fun `parseRefreshButtonProperties should handle all properties together`() {
        val json = JsonObject(mapOf(
            "text" to JsonPrimitive("Custom Refresh"),
            "color" to JsonPrimitive("#00FF00"),
            "tint" to JsonPrimitive("#0000FF"),
            "style" to JsonPrimitive("outline")
        ))
        val properties = parseRefreshButtonProperties(json)
        assertNotNull(properties)
        assertEquals("Custom Refresh", properties?.text)
        assertEquals("#00FF00", properties?.color)
        assertEquals("#0000FF", properties?.tint)
        assertEquals("outline", properties?.style)
    }
}
