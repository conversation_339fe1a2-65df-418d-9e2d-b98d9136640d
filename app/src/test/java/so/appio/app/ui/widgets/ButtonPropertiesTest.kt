package so.appio.app.ui.widgets

import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.Test
import org.junit.Assert.*

class ButtonPropertiesTest {

    @Test
    fun `parseButtonProperties should parse all properties correctly`() {
        val json = JsonObject(mapOf(
            "text" to JsonPrimitive("API Call"),
            "color" to JsonPrimitive("#FFFFFF"),
            "tint" to JsonPrimitive("#0000FF"),
            "style" to JsonPrimitive("filled"),
            "url" to JsonPrimitive("https://api.example.com/webhook")
        ))
        
        val properties = parseButtonProperties(json)
        
        assertNotNull(properties)
        assertEquals("API Call", properties?.text)
        assertEquals("#FFFFFF", properties?.color)
        assertEquals("#0000FF", properties?.tint)
        assertEquals("filled", properties?.style)
        assertEquals("https://api.example.com/webhook", properties?.url)
    }

    @Test
    fun `parseButtonProperties should handle missing url property`() {
        val json = JsonObject(mapOf(
            "text" to JsonPrimitive("Button without URL")
        ))
        
        val properties = parseButtonProperties(json)
        
        assertNotNull(properties)
        assertEquals("Button without URL", properties?.text)
        assertNull(properties?.url)
    }

    @Test
    fun `parseButtonProperties should parse all style values`() {
        val styleValues = listOf("filled", "outline", "icon-primary", "icon-secondary")

        styleValues.forEach { style ->
            val json = JsonObject(mapOf(
                "style" to JsonPrimitive(style),
                "url" to JsonPrimitive("https://api.example.com/test")
            ))
            val properties = parseButtonProperties(json)
            assertNotNull("Properties should not be null for style: $style", properties)
            assertEquals("Style should match for: $style", style, properties?.style)
        }
    }

    @Test
    fun `parseButtonProperties should handle empty json object`() {
        val json = JsonObject(emptyMap())
        
        val properties = parseButtonProperties(json)
        
        assertNotNull(properties)
        assertNull(properties?.text)
        assertNull(properties?.url)
        assertNull(properties?.style)
    }
}
