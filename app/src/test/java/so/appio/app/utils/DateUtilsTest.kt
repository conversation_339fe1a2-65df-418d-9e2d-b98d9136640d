package so.appio.app.utils

import org.junit.Test
import org.junit.Assert.*
import java.text.SimpleDateFormat
import java.util.*

class DateUtilsTest {

    @Test
    fun `test formatDate for today shows only time`() {
        val now = Date()
        val result = DateUtils.formatDate(now, wrap = false)
        
        // Should only contain time in HH:mm format, no "Yesterday" prefix
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(now)
        
        assertEquals(expectedTime, result)
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
    }

    @Test
    fun `test formatDate for today with wrap shows only time`() {
        val now = Date()
        val result = DateUtils.formatDate(now, wrap = true)
        
        // Should only contain time in HH:mm format, no "Yesterday" prefix
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(now)
        
        assertEquals(expectedTime, result)
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
    }

    @Test
    fun `test formatDate for yesterday shows Yesterday with time`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val yesterday = calendar.time
        
        val result = DateUtils.formatDate(yesterday, wrap = false)
        
        // Should contain "Yesterday" and time
        assertTrue("Result should contain 'Yesterday'", result.contains("Yesterday"))
        assertTrue("Result should contain ' - '", result.contains(" - "))
        
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(yesterday)
        assertTrue("Result should contain the time", result.contains(expectedTime))
    }

    @Test
    fun `test formatDate for yesterday with wrap shows Yesterday with newline and time`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -1)
        val yesterday = calendar.time
        
        val result = DateUtils.formatDate(yesterday, wrap = true)
        
        // Should contain "Yesterday" and time with newline
        assertTrue("Result should contain 'Yesterday'", result.contains("Yesterday"))
        assertTrue("Result should contain newline", result.contains("\n"))
        
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(yesterday)
        assertTrue("Result should contain the time", result.contains(expectedTime))
    }

    @Test
    fun `test formatDate for older date shows date and time`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -5) // 5 days ago
        val olderDate = calendar.time
        
        val result = DateUtils.formatDate(olderDate, wrap = false)
        
        // Should not contain "Yesterday"
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
        
        // Should contain date in MMM dd format and time
        val dateFormatter = SimpleDateFormat("MMM dd", Locale.getDefault())
        val expectedDate = dateFormatter.format(olderDate)
        assertTrue("Result should contain the date", result.contains(expectedDate))
        
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(olderDate)
        assertTrue("Result should contain the time", result.contains(expectedTime))
        
        assertTrue("Result should contain ' - '", result.contains(" - "))
    }

    @Test
    fun `test formatDate for older date with wrap shows date and time with newline`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -10) // 10 days ago
        val olderDate = calendar.time
        
        val result = DateUtils.formatDate(olderDate, wrap = true)
        
        // Should not contain "Yesterday"
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
        
        // Should contain date in MMM dd format and time with newline
        val dateFormatter = SimpleDateFormat("MMM dd", Locale.getDefault())
        val expectedDate = dateFormatter.format(olderDate)
        assertTrue("Result should contain the date", result.contains(expectedDate))
        
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(olderDate)
        assertTrue("Result should contain the time", result.contains(expectedTime))
        
        assertTrue("Result should contain newline", result.contains("\n"))
    }

    @Test
    fun `test formatDate for future date shows date and time`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, 3) // 3 days in the future
        val futureDate = calendar.time
        
        val result = DateUtils.formatDate(futureDate, wrap = false)
        
        // Should not contain "Yesterday"
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
        
        // Should contain date in MMM dd format and time
        val dateFormatter = SimpleDateFormat("MMM dd", Locale.getDefault())
        val expectedDate = dateFormatter.format(futureDate)
        assertTrue("Result should contain the date", result.contains(expectedDate))
        
        val timeFormatter = SimpleDateFormat("HH:mm", Locale.getDefault())
        val expectedTime = timeFormatter.format(futureDate)
        assertTrue("Result should contain the time", result.contains(expectedTime))
    }

    @Test
    fun `test formatDate for different years`() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.YEAR, -1) // Last year
        val lastYear = calendar.time
        
        val result = DateUtils.formatDate(lastYear, wrap = false)
        
        // Should not contain "Yesterday"
        assertFalse("Result should not contain 'Yesterday'", result.contains("Yesterday"))
        
        // Should contain date in MMM dd format and time
        val dateFormatter = SimpleDateFormat("MMM dd", Locale.getDefault())
        val expectedDate = dateFormatter.format(lastYear)
        assertTrue("Result should contain the date", result.contains(expectedDate))
    }

    @Test
    fun `test formatDate edge case - exactly 24 hours ago but different day`() {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 1) // Set to 1 AM today
        val today1AM = calendar.time
        
        calendar.add(Calendar.DAY_OF_YEAR, -1)
        calendar.set(Calendar.HOUR_OF_DAY, 23) // Set to 11 PM yesterday
        val yesterday11PM = calendar.time
        
        val resultToday = DateUtils.formatDate(today1AM, wrap = false)
        val resultYesterday = DateUtils.formatDate(yesterday11PM, wrap = false)
        
        // Today should not contain "Yesterday"
        assertFalse("Today's date should not contain 'Yesterday'", resultToday.contains("Yesterday"))
        
        // Yesterday should contain "Yesterday"
        assertTrue("Yesterday's date should contain 'Yesterday'", resultYesterday.contains("Yesterday"))
    }

    @Test
    fun `test formatDate with specific time values`() {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 14)
        calendar.set(Calendar.MINUTE, 30)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val specificTime = calendar.time
        
        val result = DateUtils.formatDate(specificTime, wrap = false)
        
        // Should contain the exact time
        assertTrue("Result should contain '14:30'", result.contains("14:30"))
    }
}
