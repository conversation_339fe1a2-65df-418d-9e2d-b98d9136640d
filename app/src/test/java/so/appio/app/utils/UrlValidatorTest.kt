package so.appio.app.utils

import org.junit.Test
import org.junit.Assert.*

class UrlValidatorTest {

    @Test
    fun `test valid web URL with both parameters`() {
        val url = "https://app.appio.so/?s=service123&u=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertEquals("user456", params.customerUserId)
    }

    @Test
    fun `test valid web URL with path android and with both parameters`() {
        val url = "https://app.appio.so/android/?s=service123&u=user456"
        val result = UrlValidator.validateAndParseUrl(url)

        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertEquals("user456", params.customerUserId)
    }

    @Test
    fun `test valid web URL with only service parameter`() {
        val url = "https://app.appio.so/?s=service123"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertNull(params.customerUserId)
    }

    @Test
    fun `test valid web URL with path android and with only service parameter`() {
        val url = "https://app.appio.so/android/?s=service123"
        val result = UrlValidator.validateAndParseUrl(url)

        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertNull(params.customerUserId)
    }

    @Test
    fun `test valid deep link URL with both parameters`() {
        val url = "appio://appio/?service=service123&user=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertEquals("user456", params.customerUserId)
    }

    @Test
    fun `test valid deep link URL with only service parameter`() {
        val url = "appio://appio/?service=service123"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertNull(params.customerUserId)
    }

    @Test
    fun `test web URL missing service parameter`() {
        val url = "https://app.appio.so/?u=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Error)
        val error = (result as UrlValidationResult.Error).message
        assertTrue(error.contains("Missing required parameter 's'"))
    }

    @Test
    fun `test deep link URL missing service parameter`() {
        val url = "appio://appio/?user=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Error)
        val error = (result as UrlValidationResult.Error).message
        assertTrue(error.contains("Missing required parameter 'service'"))
    }

    @Test
    fun `test invalid scheme`() {
        val url = "http://app.appio.so/?s=service123&u=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Error)
        val error = (result as UrlValidationResult.Error).message
        assertTrue(error.contains("Invalid URL format"))
    }

    @Test
    fun `test invalid host`() {
        val url = "https://example.com/?s=service123&u=user456"
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Error)
        val error = (result as UrlValidationResult.Error).message
        assertTrue(error.contains("Invalid URL format"))
    }

    @Test
    fun `test empty URL`() {
        val url = ""
        val result = UrlValidator.validateAndParseUrl(url)
        
        assertTrue(result is UrlValidationResult.Error)
        val error = (result as UrlValidationResult.Error).message
        assertTrue(error.contains("URL cannot be empty"))
    }

    @Test
    fun `test isValidAppioUrl convenience method`() {
        assertTrue(UrlValidator.isValidAppioUrl("https://app.appio.so/?s=service123&u=user456"))
        assertTrue(UrlValidator.isValidAppioUrl("appio://appio/?service=service123&user=user456"))
        assertFalse(UrlValidator.isValidAppioUrl("https://example.com/?s=service123"))
        assertFalse(UrlValidator.isValidAppioUrl(""))
    }

    @Test
    fun `test empty parameter values are treated as null`() {
        val url = "https://app.appio.so/?s=service123&u="
        val result = UrlValidator.validateAndParseUrl(url)

        assertTrue(result is UrlValidationResult.Success)
        val params = (result as UrlValidationResult.Success).params
        assertEquals("service123", params.serviceId)
        assertNull(params.customerUserId)
    }

    @Test
    fun `test isValidBrowserUrl with valid URLs`() {
        // Note: In unit test environment, Android's URLUtil and Patterns may not work properly
        // These tests verify the method doesn't crash and handles basic cases
        val result1 = UrlValidator.isValidBrowserUrl("https://www.google.com")
        val result2 = UrlValidator.isValidBrowserUrl("http://example.com")
        val result3 = UrlValidator.isValidBrowserUrl("https://www.google.com/search?q=test")

        // In unit tests, these might return false due to missing Android framework
        // but they should not crash
        assertNotNull("Method should not return null", result1)
        assertNotNull("Method should not return null", result2)
        assertNotNull("Method should not return null", result3)
    }

    @Test
    fun `test isValidBrowserUrl with invalid URLs`() {
        assertFalse(UrlValidator.isValidBrowserUrl(""))
        assertFalse(UrlValidator.isValidBrowserUrl("   "))
        assertFalse(UrlValidator.isValidBrowserUrl("not-a-url"))
        assertFalse(UrlValidator.isValidBrowserUrl("mailto:<EMAIL>"))
        assertFalse(UrlValidator.isValidBrowserUrl("tel:+1234567890"))
        assertFalse(UrlValidator.isValidBrowserUrl("https://"))
        assertFalse(UrlValidator.isValidBrowserUrl("http://"))
    }

    @Test
    fun `test extractMainDomain with valid URLs`() {
        assertEquals("www.google.com", UrlValidator.extractMainDomain("https://www.google.com"))
        assertEquals("example.com", UrlValidator.extractMainDomain("http://example.com/path?query=value"))
        assertEquals("subdomain.example.com", UrlValidator.extractMainDomain("https://subdomain.example.com/path"))
        assertEquals("app.appio.so", UrlValidator.extractMainDomain("https://app.appio.so/?s=test&u=user"))
    }

    @Test
    fun `test extractMainDomain with invalid URLs`() {
        assertNull(UrlValidator.extractMainDomain(""))
        assertNull(UrlValidator.extractMainDomain("not-a-url"))
        assertNull(UrlValidator.extractMainDomain("https://"))
    }
}
