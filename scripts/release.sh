#!/opt/homebrew/bin/bash

# Colors
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

print_info() {
  echo -e ""
  echo -e "${BLUE}==================================================================${NC}"
  echo -e "${BLUE} ℹ️   $1${NC}"
  echo -e "${BLUE}==================================================================${NC}"
  echo -e ""
}

print_success() {
  echo -e ""
  echo -e "${GREEN}==================================================================${NC}"
  echo -e "${GREEN} ✅   $1${NC}"
  echo -e "${GREEN}==================================================================${NC}"
  echo -e ""
}

print_error() {
  echo -e ""
  echo -e "${RED}==================================================================${NC}"
  echo -e "${RED} ❌   $1${NC}"
  echo -e "${RED}==================================================================${NC}"
  echo -e ""
}

#################################################################

print_info "Building release..."
./gradlew clean bundleRelease


print_info "Zip debug symbols..."
cd app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib
zip -r native-debug-symbols.zip .
cd -


print_info "Deploy to Google Play..."
fastlane supply \
  --aab ./app/build/outputs/bundle/release/app-release.aab \
  --track production \
  --json_key ./.keys/google-play-store.json \
  --package_name so.appio.app \
  --mapping_paths \
    ./app/build/outputs/mapping/release/mapping.txt \
    ./app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib/native-debug-symbols.zip
# or call: fastlane android deploy

if [ $? -ne 0 ]; then
  print_error "Release failed"
  exit 1
fi

print_success "Release completed!"
