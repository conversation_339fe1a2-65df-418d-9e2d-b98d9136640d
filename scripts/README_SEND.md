# Scripts Folder

This folder contains various scripts for the project.

NOTE: FCM token is linked with account build in app via google-services.json which is different per environment.

# Firebase accounts

Manage accounts via: https://console.firebase.google.com/
Account: `<EMAIL>`

We have 2 accounts setup for Appio app:

- [Appio Development]()
  - Sender ID: ***********

- [Appio Production](https://console.firebase.google.com/u/0/project/appio-production/overview)
  - Sender ID: ************


## Available Scripts

### 🔔 Push Notification Script

#### `send_dev.sh`, `send_prod.sh`
Advanced script using Firebase Admin SDK with OAuth2 authentication.
**Pure shell implementation - no Python dependency required!**

**Prerequisites:**
- `curl` (for HTTP requests)
- `openssl` (for JWT signing)
- `base64` (for encoding)
- `jq` (optional, for better JSON parsing)

**Usage:**
```bash
# Method 1: Edit the script and update FCM_TOKEN variable
./scripts/send_dev.sh

# Method 2: Pass token as argument
./scripts/send_dev.sh "your_fcm_token_here"
```

## 📱 How to Get FCM Token

1. **Run your Android app** in the emulator
2. **Check logcat** for one of these messages:
   ```
   Refreshed token: [YOUR_TOKEN]
   Current FCM Token: [YOUR_TOKEN]
   ```
3. **Copy the token** (long string starting with letters/numbers)
4. **Use the token** in the scripts above

### Getting FCM Token via ADB:
```bash
# Filter logcat for FCM tokens
adb logcat | grep -E "(Refreshed token|Current FCM Token)"
```

## 🚀 Quick Start

1. **Ensure prerequisites are installed** (most systems have these by default):
   ```bash
   # Check if tools are available
   which curl openssl base64

   # Optional: Install jq for better JSON parsing
   # macOS: brew install jq
   # Ubuntu: apt-get install jq
   ```

2. **Run your Android app** and get the FCM token from logcat

3. **Send a test notification**:
   ```bash
   ./scripts/send_dev.sh "your_fcm_token_here"
   ```

## 🛠 Troubleshooting

- **"openssl not found"**: Install OpenSSL (`brew install openssl` on macOS)
- **"curl not found"**: Install curl (usually pre-installed on most systems)
- **"Unauthorized" error**: Check your server key or service account file
- **"Invalid token" error**: Get a fresh FCM token from your app
- **"Failed to generate JWT token"**: Check service account file permissions and format
- **No notification received**: Check app permissions and notification channels
