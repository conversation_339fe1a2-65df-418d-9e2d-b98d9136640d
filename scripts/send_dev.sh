#!/opt/homebrew/bin/bash

# ==============================================================================
# Firebase Push Notification Script
# ==============================================================================
# This script sends push notifications to your Android emulator app via Firebase
#
# Usage:
#   - ./send_dev.sh "***_fcm_token_***"
#   - ./send_dev.sh          // this will use FCM_TOKEN constant from the script
#
# ==============================================================================

# Configuration Constants
readonly FIREBASE_PROJECT_ID="appio-so"
readonly SERVICE_ACCOUNT_FILE=".keys/firebase-dev.json"
RANDOM_ID=$((RANDOM % 90000 + 10000))  # generates 10000–99999

# Custom Data
declare -A CUSTOM_DATA
CUSTOM_DATA["service_id"]="demo_svc_01k0vp83g0pxxt1d2fx9csdmq8"
CUSTOM_DATA["notification_id"]="ntf_000000000000000000000$RANDOM_ID"

## Background
#CUSTOM_DATA["type"]="background"

## Foreground
CUSTOM_DATA["type"]="foreground" # not required
CUSTOM_DATA["service_title"]="Zesty Service"
CUSTOM_DATA["logo_url"]="https://cdn.appio.so/app/demo.appio.so/logo.png"
#CUSTOM_DATA["image_url"]="https://cdn.appio.so/app/demo.appio.so/banner.png"
#CUSTOM_DATA["link"]="https://appio.so"
#CUSTOM_DATA["action_category"]="APPIO_YES_NO"

# TODO: live activity. not fully implemented.
# Requires permissions for: location or dataSync o mediaPlayback
## Live activity, must have priority: high
#CUSTOM_DATA["type"]="live:start"
#CUSTOM_DATA["type"]="live:update"
#CUSTOM_DATA["type"]="live:stop"

# Optional
#CUSTOM_DATA["category"]="msg"
#FCM_TOKEN=""

# ==============================================================================

# Source the core functionality
source "$(dirname "$0")/send_core.sh"

# Run the main function with all arguments
main "$@"