#!/opt/homebrew/bin/bash

# ==============================================================================
# Firebase Push Notification Core Script
# ==============================================================================
# This is the core functionality for sending Firebase push notifications
# Not meant to be called directly - use send_dev.sh or other environment scripts
# ==============================================================================

# Prevent direct execution
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "Error: This script should not be called directly."
    echo "Please use send_dev.sh or another environment-specific script instead."
    exit 1
fi

# Script Constants
readonly FCM_API_URL="https://fcm.googleapis.com/v1/projects/${FIREBASE_PROJECT_ID}/messages:send"

# Notification Configuration
readonly NOTIFICATION_TITLE="Notification: $(date '+%d/%m at %H:%M:%S')"
readonly NOTIFICATION_BODY="Hello from Firebase! This is a test notification."

# NOTE: This script sends DATA-ONLY messages (no notification payload)
# This ensures MyFirebaseMessagingService.onMessageReceived() is ALWAYS called,
# even when the app is killed/background, allowing custom cached icon to be used.


# Colors
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# =============================================================================
# Helper Functions
# =============================================================================

# Build JSON data payload from custom data array
build_data_payload() {
    local data_json=""

    # Always include title and body
    data_json="\"title\": \"$NOTIFICATION_TITLE\", \"body\": \"$NOTIFICATION_BODY\""

    # Add custom data if CUSTOM_DATA array exists and has elements
    if declare -p CUSTOM_DATA &>/dev/null; then
#        print_info "Found CUSTOM_DATA array with ${#CUSTOM_DATA[@]} entries"
#        print_info "Keys: ${!CUSTOM_DATA[@]}"
        for key in "${!CUSTOM_DATA[@]}"; do
            # Escape quotes in values for JSON safety
            local escaped_value="${CUSTOM_DATA[$key]//\"/\\\"}"
            data_json="$data_json, \"$key\": \"$escaped_value\""
#            print_info "Added: $key = $escaped_value"
        done
    else
        print_info "No CUSTOM_DATA array found"
    fi

    echo "$data_json"
}

print_header() {
    echo -e "${BLUE}==================================================================${NC}"
    echo -e "${BLUE}  Firebase Push Notification${NC}"
    echo -e "${BLUE}==================================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check if required tools are installed
check_dependencies() {
    local missing_deps=()

    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi

    if ! command -v openssl &> /dev/null; then
        missing_deps+=("openssl")
    fi

    if ! command -v base64 &> /dev/null; then
        missing_deps+=("base64")
    fi

    if ! command -v jq &> /dev/null; then
        print_warning "jq not found - JSON formatting will be basic"
    fi

    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_error "Missing required dependencies: ${missing_deps[*]}"
        print_info "Please install the missing dependencies and try again."
        print_info "On macOS: brew install curl openssl"
        print_info "On Ubuntu/Debian: apt-get install curl openssl"
        exit 1
    fi
}

# Validate service account file exists
check_service_account() {
    if [ ! -f "$SERVICE_ACCOUNT_FILE" ]; then
        print_error "Service account file not found: $SERVICE_ACCOUNT_FILE"
        print_info "Please ensure the Firebase service account JSON file exists."
        exit 1
    fi
    print_success "Service account file found"
}

# Helper function to extract JSON values using shell commands
extract_json_value() {
    local json_file="$1"
    local key="$2"

    if command -v jq &> /dev/null; then
        jq -r ".$key" "$json_file"
    else
        # Fallback using grep and sed for basic JSON parsing
        grep "\"$key\"" "$json_file" | sed 's/.*"'"$key"'"[[:space:]]*:[[:space:]]*"\([^"]*\)".*/\1/'
    fi
}

# Base64 URL encode function
base64url_encode() {
    local input="$1"
    echo -n "$input" | base64 | tr '+/' '-_' | tr -d '='
}

# Generate JWT token using shell commands only
generate_jwt_token() {
    # Extract values from service account JSON
    local client_email=$(extract_json_value "$SERVICE_ACCOUNT_FILE" "client_email")
    local private_key_raw=$(extract_json_value "$SERVICE_ACCOUNT_FILE" "private_key")

    if [ -z "$client_email" ] || [ -z "$private_key_raw" ]; then
        echo "ERROR: Failed to extract client_email or private_key from service account file" >&2
        return 1
    fi

    # Create temporary private key file
    local temp_key_file=$(mktemp)
    echo -e "$private_key_raw" > "$temp_key_file"

    # Current timestamp
    local now=$(date +%s)
    local exp=$((now + 3600))

    # Create JWT header and payload
    local header='{"alg":"RS256","typ":"JWT"}'
    local payload="{\"iss\":\"$client_email\",\"scope\":\"https://www.googleapis.com/auth/firebase.messaging\",\"aud\":\"https://oauth2.googleapis.com/token\",\"iat\":$now,\"exp\":$exp}"

    # Encode header and payload
    local header_encoded=$(base64url_encode "$header")
    local payload_encoded=$(base64url_encode "$payload")

    # Create message to sign
    local message="$header_encoded.$payload_encoded"

    # Sign the message using OpenSSL
    local signature=$(echo -n "$message" | openssl dgst -sha256 -sign "$temp_key_file" 2>/dev/null | base64 | tr '+/' '-_' | tr -d '=')

    # Clean up temporary file
    rm -f "$temp_key_file"

    if [ -z "$signature" ]; then
        echo "ERROR: Failed to generate signature with OpenSSL" >&2
        return 1
    fi

    # Return complete JWT token
    echo "$message.$signature"
}

# Exchange JWT for OAuth2 access token
get_access_token() {
    # Generate JWT token
    local jwt_token=$(generate_jwt_token)
    if [ $? -ne 0 ] || [ -z "$jwt_token" ]; then
        echo "ERROR: Failed to generate JWT token" >&2
        return 1
    fi

    # Exchange JWT for access token
    local response=$(curl -s -X POST \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=urn:ietf:params:oauth:grant-type:jwt-bearer&assertion=$jwt_token" \
        "https://oauth2.googleapis.com/token")

    # Extract access token from response
    local access_token
    if command -v jq &> /dev/null; then
        access_token=$(echo "$response" | jq -r '.access_token // empty')
    else
        access_token=$(echo "$response" | grep -o '"access_token":"[^"]*"' | sed 's/"access_token":"\([^"]*\)"/\1/')
    fi

    if [ -z "$access_token" ] || [ "$access_token" = "null" ]; then
        echo "ERROR: Failed to get access token" >&2
        echo "ERROR: OAuth2 response: $response" >&2
        return 1
    fi

    echo "$access_token"
}

# Send push notification
send_notification() {
    local token="$1"
    local access_token="$2"

    print_info "Sending push notification..."
    print_info "Target token: ${token:0:20}..."

    # Create JSON payload - DATA ONLY (no notification payload)
    # This ensures onMessageReceived() is always called, even when app is killed
    # Documentation: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

    # Build dynamic data payload
    local data_payload=$(build_data_payload)

#    print_info "Final data payload: $data_payload"

    local json_payload=$(cat <<EOF
{
  "message": {
    "token": "$token",
    "data": {
      $data_payload
    },
    "android": {
      "priority": "high"
    }
  }
}
EOF
)

    print_info "Complete JSON payload:"
    echo "$json_payload" | jq

    # Send HTTP request
    print_info "Sending request to: $FCM_API_URL"
    print_info "Access token: ${access_token:0:20}..."
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $access_token" \
        -H "Content-Type: application/json" \
        -d "$json_payload" \
        "$FCM_API_URL")

    local http_code=$(echo "$response" | tail -n1)
    local response_body=$(echo "$response" | sed '$d')

    if [ "$http_code" = "200" ]; then
        print_success "Push notification sent successfully!"
        print_info "Response: $response_body"

        # Try to extract message name from response for confirmation
        local message_name
        if command -v jq &> /dev/null; then
            message_name=$(echo "$response_body" | jq -r '.name // empty')
        else
            message_name=$(echo "$response_body" | grep -o '"name":"[^"]*"' | sed 's/"name":"\([^"]*\)"/\1/')
        fi

        if [ -n "$message_name" ]; then
            print_success "Message ID: $message_name"
        fi
    else
        print_error "Failed to send push notification (HTTP $http_code)"
        print_error "Response: $response_body"

        # Common error explanations
        case $http_code in
            400)
                print_info "Bad Request - Check your JSON payload and token format"
                ;;
            401)
                print_info "Unauthorized - Check your service account credentials"
                ;;
            403)
                print_info "Forbidden - Check Firebase project permissions"
                ;;
            404)
                print_info "Not Found - Check the FCM API URL and project ID"
                ;;
        esac

        exit 1
    fi
}

# =============================================================================
# Main Script
# =============================================================================

main() {
    print_header

    # Use token from argument if provided
    if [ $# -eq 1 ]; then
        FCM_TOKEN="$1"
        print_info "Using FCM token from argument"
    fi

    # Validate FCM token
    if [ "$FCM_TOKEN" = "YOUR_DEVICE_TOKEN_HERE" ] || [ -z "$FCM_TOKEN" ]; then
        print_error "FCM token not set!"
        print_info "Please either:"
        print_info "1. Update the FCM_TOKEN variable in this script, or"
        print_info "2. Pass the token as an argument: $0 \"your_token_here\""
        print_info ""
        print_info "To get your FCM token:"
        print_info "1. Run your Android app in the emulator"
        print_info "2. Check the logcat for 'Refreshed token:' or 'Current FCM Token:'"
        print_info "3. Copy the token and use it with this script"
        exit 1
    fi

    # Run checks
    check_dependencies
    check_service_account

    # Get access token and send notification
    print_info "Generating OAuth2 access token..."
    local access_token=$(get_access_token)
    if [ $? -ne 0 ] || [ -z "$access_token" ]; then
        print_error "Failed to get access token"
        exit 1
    fi

    print_success "Access token generated successfully"
    send_notification "$FCM_TOKEN" "$access_token"

    print_success "Script completed successfully!"
}
