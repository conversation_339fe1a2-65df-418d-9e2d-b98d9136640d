# Sentry Integration Setup

This document describes how to complete the Sentry monitoring integration for the Appio Android app.

## Current Status

✅ **Completed:**
- Sentry Android Gradle Plugin 5.8.0 integrated (includes Sentry Android SDK 8.14.0)
- Automatic SDK dependency management
- AndroidManifest.xml configuration for Sentry
- ProGuard/R8 mapping file upload capability
- Runtime configuration in `MyApplication.kt`
- ProGuard rules for Sentry
- Build verification completed successfully
- **Comprehensive error reporting integrated:**
  - Global uncaught exception handler
  - API error reporting with context
  - Database error reporting
  - Widget error reporting
  - Firebase/FCM error reporting
  - ViewModel error reporting
  - QR scanner error reporting

⚠️ **Needs Configuration:**
- Sentry DSN (Data Source Name) from your Sentry project

## Setup Steps

### 1. Create a Sentry Account and Project

1. Go to [sentry.io](https://sentry.io) and create an account (if you don't have one)
2. Create a new project and select "Android" as the platform
3. Copy the DSN from your project settings

### 2. Configure the DSN

1. Open `app/src/main/AndroidManifest.xml`
2. Find the line with the placeholder DSN:
   ```xml
   <meta-data
       android:name="io.sentry.dsn"
       android:value="https://<EMAIL>/your-project-id" />
   ```
3. Replace it with your actual Sentry DSN

### 3. Test the Integration

Once you've configured your DSN, the integration will automatically start capturing errors. To verify it's working:

1. **Build and run the app** in debug mode
2. **Check your Sentry dashboard** for incoming events
3. **Trigger an error** (e.g., navigate to a non-existent service) to test error reporting
4. **Check Android logs** for Sentry configuration messages:
   ```
   LOG:MyApplication: Sentry configuration completed successfully
   ```

### 4. Verify Integration

1. Run the app in debug mode
2. Check the Android logs for Sentry configuration messages:
   ```
   LOG:MyApplication: Sentry configuration completed successfully
   ```
3. Check your Sentry dashboard for incoming events

### 5. Production Configuration

Once you've verified Sentry is working:

1. **Environment configuration**: The Gradle plugin automatically sets:
   - Environment: "debug" for debug builds, "release" for release builds
   - Release version: Automatically set from your app version
   - ProGuard mapping files: Can be automatically uploaded for release builds (requires auth token)
3. **Performance monitoring**: Currently set to 10% sampling rate (configurable in `AndroidManifest.xml`)

## Configuration Options

The current Sentry configuration includes:

### Gradle Plugin Configuration (`app/build.gradle.kts`):
- **Automatic SDK installation**: Sentry Android SDK 8.16.0 automatically included
- **ProGuard mapping upload**: Available (requires auth token configuration)
- **Source context upload**: Available (requires auth token configuration)

### AndroidManifest.xml Configuration:
- **DSN**: Your Sentry project identifier
- **User interaction tracking**: Enabled for breadcrumbs
- **Screenshot on crash**: Enabled for better debugging
- **View hierarchy on crash**: Enabled for UI debugging
- **Performance monitoring**: 10% sampling rate (configurable)
- **Session replay**: 100% on errors, 10% on sessions

### Application Configuration (`MyApplication.kt`):
- **Custom tags**: Build type, app component identification
- **Extra context**: App version information
- **Debug utilities**: Test message capabilities

## Troubleshooting

### Common Issues

1. **No events in Sentry dashboard**:
   - Verify the DSN is correct
   - Check Android logs for Sentry initialization errors
   - Ensure you're testing in debug mode first

2. **ProGuard issues in release builds**:
   - The necessary ProGuard rules are already added in `app/proguard-rules.pro`
   - If you encounter issues, check the ProGuard mapping files

3. **Performance impact**:
   - Sentry is configured with conservative sampling rates
   - Performance monitoring can be disabled by setting `tracesSampleRate = 0.0`

### Debug Commands

Use these log filters to see Sentry-related logs:
```bash
adb logcat | grep -E "(Sentry|LOG:MyApplication|LOG:SentryTestUtils)"
```

## Comprehensive Error Reporting Features

The integration now includes comprehensive error reporting across all app components:

### **Automatic Error Capture:**
- **Global Exception Handler**: Catches all uncaught exceptions and crashes
- **API Errors**: Network failures, timeouts, and server errors with request context
- **Database Errors**: Room database operation failures with table/operation context
- **Widget Errors**: Glance widget composition and configuration errors
- **Firebase Errors**: FCM token refresh and message processing failures
- **ViewModel Errors**: Business logic errors in ViewModels with component context
- **UI Errors**: QR scanner and other UI component failures

### **Error Context Information:**
Each error type includes relevant context:
- **API Errors**: HTTP method, URL, status code, response body
- **Database Errors**: Operation type, table name, entity ID
- **Widget Errors**: Widget ID, service ID, operation type, widget configuration
- **Firebase Errors**: Operation type, message ID
- **General Errors**: Component name, operation name

### **Error Categories and Tags:**
Errors are automatically tagged for easy filtering in Sentry:
- `error.type`: api_error, database_error, widget_error, firebase_error, app_error
- `error.category`: network, data, ui, messaging, general
- `app.component`: Component name where error occurred
- `build.type`: debug or release

## Next Steps

After basic integration is working, consider:

1. **User context**: Add user identification for better error tracking
2. **Custom tags**: Add app-specific tags for better filtering
3. **Performance monitoring**: Fine-tune performance sampling rates
4. **Release health**: Monitor app stability and crash-free sessions
5. **Custom breadcrumbs**: Add custom breadcrumbs for better debugging context

## Security Notes

- The DSN is not sensitive and can be included in your app
- Sentry automatically scrubs sensitive data from error reports
- Consider adding custom data scrubbing rules if needed
