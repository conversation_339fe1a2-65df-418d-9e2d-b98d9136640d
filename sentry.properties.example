# Sentry Configuration for Gradle Plugin
# This file is REQUIRED for ProGuard mapping uploads and other build-time features
#
# Docs: https://docs.sentry.io/platforms/android/configuration/gradle/#proguardr8--dexguard
#
# IMPORTANT: This file contains sensitive information and should NOT be committed to git
# Add this file to .gitignore

# Required: Your Sentry organization slug
# You can find this in your Sentry dashboard URL: https://sentry.io/organizations/YOUR_ORG_SLUG/
# You need to find your organization SLUG (not ID) from your Sentry dashboard
defaults.org=appio-so

# Required: Your Sentry project name
# This should match the project name in your Sentry dashboard
# You need to find your project NAME (not ID) from your Sentry dashboard
defaults.project=android

# Required for uploads: Auth token
# Generate this in Sentry: Settings > Developer Settings > Organization Tokens
# Or: https://appio-so.sentry.io/settings/auth-tokens/
# The token needs 'project:releases' and 'project:write' scopes
# Set this to enable ProGuard mapping uploads
auth.token=

# Optional: Base URL (only needed if using self-hosted Sentry)
# For SaaS Sentry, leave this commented out
# defaults.url=https://your-sentry-instance.com/
