# NOTE: app must be built and native symbols file zipped
# NOTE: release notes are taken from ./fastlane/metadata/android/en-US/changelogs/default.txt

opt_out_usage

# default_platform(:android)
#
# # Constants for file paths
# SERVICE_CRED_FILE_PATH = "./.keys/google-play-store.json"
# SYMBOL_FILE_PATH = "./app/build/intermediates/merged_native_libs/release/mergeReleaseNativeLibs/out/lib/native-debug-symbols.zip"
# MAPPING_FILE_PATH = "./app/build/outputs/mapping/release/mapping.txt"
# AAB_FILE_PATH = "./app/build/outputs/bundle/release/app-release.aab"
#
# platform :android do
#   lane :deploy do
#     upload_to_play_store(
#       json_key: SERVICE_CRED_FILE_PATH,
#       track: "production",
#       aab: AAB_FILE_PATH,
#       package_name: "so.appio.app",
#       mapping_paths: [SYMBOL_FILE_PATH, MAPPING_FILE_PATH],
#     )
#   end
# end