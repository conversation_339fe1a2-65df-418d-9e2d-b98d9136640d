# Notification Permissions Flow

This document describes the notification permission handling flow in the Appio Android app.

- Android < 13 (API 31...33-) has only system-level notification settings.
- Android 13+ (API 33+) has runtime notification permissions and system-level notification settings. Both needs to be enabled for app to receive notifications.

## Overview

The notification permission system handles:
- **Android 13+ (API 33+)**: Runtime notification permissions with native and custom dialogs
- **Android < 13**: System-level notification settings only
- **Permission state persistence**: Tracking whether user was asked before to provide better UX
- **Dialog management**: Choosing between native Android dialogs vs custom app dialogs
- **ServiceScreen integration**: Permissions are checked only on ServiceScreen when preview modal is dismissed

## Permission States

### Android 13+ (API 33+)

The app tracks two key states:
1. **Permission granted**: Runtime permission status
2. **Notifications enabled**: System-level notification setting

**Combined states:**
- **Fully enabled**: Permission granted AND notifications enabled
- **Permission only**: Permission granted BUT notifications disabled in settings
- **Disabled**: Permission not granted

### Android < 13

Only tracks:
- **Notifications enabled**: System-level notification setting

## General Flow

### ServiceScreen with Preview Modal
```
ServiceScreen loads → showPreview modal appears → User dismisses modal → Permission check triggered
```

**Important**: Permission requests are **only** triggered on ServiceScreen when `service.showPreview` changes from `true` to `false`. This happens when:
1. User visits a service for the first time (showPreview starts as true)
2. User dismisses the preview modal
3. The LaunchedEffect in ServiceScreen detects showPreview became false and calls `notificationPermissionManager.request()`

**App Resume Behavior**: When app resumes, `MainActivity.onResume()` calls `checkAndUpdateNotificationStatus()` which updates the notification status in DataStore but does **not** trigger permission request dialogs.

### Permission Check Logic (Android 13+)

```
ServiceScreen preview modal dismissed (showPreview becomes false)
    ↓
Check permission & notification states
    ↓
┌─────────────────────────────────────────────────────────┐
│                    Decision Logic                       │
├─────────────────────────────────────────────────────────┤
│ 1. Fully Enabled                                       │
│    → Reset "asked before" flag                         │
│    → Enable notifications                               │
│                                                         │
│ 2. First Time (never asked)                            │
│    → Mark as "asked before"                             │
│    → Show native permission dialog                     │
│                                                         │
│ 3. Asked Before + Can ask again                        │
│    → Show native permission dialog                     │
│                                                         │
│ 4. Asked Before + Permanently denied                   │
│    → Show custom dialog (go to settings)               │
│                                                         │
│ 5. Permission granted but notifications disabled       │
│    → Show custom dialog (go to settings)               │
└─────────────────────────────────────────────────────────┘
```

### Dialog Types

**Native Permission Dialog:**
- Shown for first-time requests and when user can be asked again (user revokes previously granted permission)
- Handled by Android system
- Result: User grants or denies permission

**Custom Dialog:**
- Shown when native dialog can't be used (permanently denied, settings issue)
- Directs user to device settings
- Includes "skip this time" logic to avoid spam

## User Scenarios

Permissions are checked only on ServiceScreen when the preview modal is dismissed (when `service.showPreview` becomes false).
Never showing both native and custom dialogs in a row.

### 1. First Time User
- Never been asked for permission
- **Result**: Native permission dialog shows

### 2. Permission Previously Denied, Can Ask Again
- User denied before but Android allows asking again
- **Result**: Native permission dialog shows

### 3. Permission Permanently Denied
- User denied and selected "Don't ask again"
- **Result**: Custom dialog shows (directs to settings)

### 4. Permission Granted but Notifications Disabled
- Permission granted but user disabled notifications in device settings
- **Result**: Custom dialog shows (directs to settings)

### 5. User Revokes Permission After Granting
- User granted permission, then revoked it in device settings
- **Result**: Native permission dialog shows again

### 6. Android < 13
- No runtime permissions, only system notification settings
- **Result**: Custom dialog if notifications disabled

## Key Concepts

### "Asked Before" Flag
- **Purpose**: Track if user was previously asked for permission
- **Reset when**: Permission is granted (allows re-asking after revocation)
- **Why needed**: Android can't distinguish between "first time" and "permanently denied"

### Skip Custom Dialog Logic
- **Purpose**: Prevent showing custom dialog immediately after user denies native permission
- **Behavior**: Custom dialog is only shown when permission is permanently denied or when permission is granted but notifications are disabled in settings
- **Why needed**: Avoid double-asking (native dialog and custom dialog at the same time) and spam
- **How**: When user denies native permission, the permission result handler doesn't trigger another permission check, so custom dialog won't show until next ServiceScreen visit.

### Settings Flow
- **Custom dialog**: Directs user to device settings directly to enable app's notifications
- **Return handling**: On app resume, `checkAndUpdateNotificationStatus()` is called to update the DataStore, but permission dialogs are only shown when visiting ServiceScreen again
- **Re-check**: Permission status is automatically updated in DataStore on app resume, but permission request dialogs are only triggered on ServiceScreen when preview modal is dismissed

## Why This Complexity?

The permission system is complex because:

1. **Android limitation**: Can't distinguish first-time vs permanently denied
2. **Better UX**: Show native dialog when possible, custom when necessary. Close custom dialog when returning from settings back to app.
3. **Avoid spam**: Don't repeatedly ask users who clearly don't want notifications
4. **Handle revocation**: Allow users to re-enable notifications easily