# Android Widget Template JSON Documentation

This documentation describes the widget elements supported by the Appio Android widget system and how to configure them using JSON. Each element maps to a Jetpack Glance composable and has customizable properties.

## WidgetTemplate

Main object representing the widget template configuration.

| Property | Type | Description |
|----------|------|-------------|
| `variants` | `[WidgetVariant]` | Array of [widget variants](#widgetvariant) for different sizes |

## WidgetVariant

Represents a widget configuration for a specific size range.

| Property | Type | Description |
|----------|------|-------------|
| `properties` | `WidgetVariantProperties` | [Variant-level properties](#widgetvariantproperties) |
| `elements` | `[WidgetElement]` | Array of [widget elements](#widgetelement) |

## WidgetVariantProperties

Properties that apply to the entire widget variant.

| Property | Type | Description |
|----------|------|-------------|
| `version` | `String?` | Configuration version |
| `width` | `Int?` | Minimum width in dp |
| `height` | `Int?` | Minimum height in dp |
| `background` | `String?` | Background [Color](#color) |
| `url` | `String?` | The URL to open when clicked on a widget |

## WidgetElement

Main object representing a widget element.

| Property | Type | Description |
|----------|------|-------------|
| `type` | `String` | [Widget Element Type](#widget-element-types) |
| `properties` | `Object` | Element properties. Each type has its own set of properties. |
| `elements` | `[WidgetElement]` | Nested elements (for container types) |

---
## Widget Element Types

- [box](#box)
- [column](#column)
- [row](#row)
- [image](#image)
- [text](#text)
- [spacer](#spacer)
- [gauge](#gauge)

- [button](#button) - custom, non-native
- [refreshButton](#refreshButton) - custom, non-native
- [lastUpdated](#lastUpdated) - custom, non-native

---
## box

Container that stacks child elements on top of each other (similar to SwiftUI ZStack).

| Property | Type | Description |
|----------|------|-------------|
| `horizontalAlignment` | `String?` | Horizontal alignment: `start`, `center`, `end` |
| `verticalAlignment` | `String?` | Vertical alignment: `top`, `center`, `bottom` |
| `background` | `String?` | Background [Color](#color) |
| `cornerRadius` | `Int?` | Corner radius in dp |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |
| `elements` | `[WidgetElement]` | Child elements |

---
## column

Arranges child elements vertically (similar to SwiftUI VStack).

| Property | Type | Description |
|----------|------|-------------|
| `horizontalAlignment` | `String?` | Horizontal alignment: `start`, `center`, `end` |
| `verticalAlignment` | `String?` | Vertical alignment: `top`, `center`, `bottom` |
| `spacing` | `Int?` | Space between elements in dp |
| `background` | `String?` | Background [Color](#color) |
| `cornerRadius` | `Int?` | Corner radius in dp |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |
| `elements` | `[WidgetElement]` | Child elements |

---
## row

Arranges child elements horizontally (similar to SwiftUI HStack).

| Property | Type | Description |
|----------|------|-------------|
| `horizontalAlignment` | `String?` | Horizontal alignment: `start`, `center`, `end` |
| `verticalAlignment` | `String?` | Vertical alignment: `top`, `center`, `bottom` |
| `spacing` | `Int?` | Space between elements in dp |
| `background` | `String?` | Background [Color](#color) |
| `cornerRadius` | `Int?` | Corner radius in dp |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |
| `elements` | `[WidgetElement]` | Child elements |

---
## text

Displays a text label.

| Property | Type | Description |
|----------|------|-------------|
| `text` | `String` | **Required**: Text content |
| `fontSize` | `Int?` | Font size in sp |
| `fontWeight` | `String?` | Font weight: `normal`, `medium`, `semibold`, `bold` |
| `color` | `String?` | Text [Color](#color) |
| `background` | `String?` | Background [Color](#color) |
| `alignment` | `String?` | Text alignment: `start`, `center`, `end`, `left`, `right` |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |

---
## image

Displays an image from a URL.

| Property | Type | Description |
|----------|------|-------------|
| `src` | `String?` | Image URL |
| `cornerRadius` | `Int?` | Corner radius in dp |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |

**Note**: The internal `__src` property is automatically generated during preprocessing and contains the cached image filename.

---
## spacer

Adds flexible spacing.

| Property | Type | Description |
|----------|------|-------------|
| `length` | `Int\|String?` | Size in dp or `max` for fillMaxSize |
| `padding` | `Padding?` | [Padding](#padding) |

---
## gauge

Displays a gauge visualization using a pre-rendered image.

| Property | Type           | Description |
|----------|----------------|-------------|
| `label` | `String?`      | Text label displayed over the gauge |
| `value` | `Float?`       | Gauge value 0-100 (converted to 0.0-1.0 for rendering) |
| `color` | `String?`      | Label text [Color](#color) |
| `tint` | `String?`      | Gauge tint color (used for image generation) |
| `fontSize` | `Int?`         | Label font size in sp |
| `fontWeight` | `String?`      | Label font weight: `normal`, `medium`, `semibold`, `bold` |
| `size` | `Int\|String?` | Size in dp or `max` for fillMaxSize |
| `padding` | `Padding?`     | [Padding](#padding) |

**Note**: The internal `__src` property is automatically generated during preprocessing and contains the cached gauge image filename.

---
## refreshButton

Displays a button to trigger widget refresh. This is a custom, non-native element.

| Property | Type | Description |
|----------|------|-------------|
| `text` | `String?` | Button label (default: `Refresh`) |
| `color` | `String?` | Text [Color](#color) |
| `tint` | `String?` | Background [Color](#color) |
| `style` | `String?` | Button style: `filled`, `outline`, `icon-primary`, `icon-secondary` |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |

---
## button

Displays a button that triggers an call to a specified URL. This is a custom, non-native element.

| Property | Type | Description |
|----------|------|-------------|
| `text` | `String?` | Button label (default: `Button`) |
| `color` | `String?` | Text [Color](#color) |
| `tint` | `String?` | Background [Color](#color) |
| `style` | `String?` | Button style: `filled`, `outline`, `icon-primary`, `icon-secondary` |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |
| `url` | `String?` | URL to call when button is pressed |

**Note**: When the button is pressed, if a `url` is provided, a GET request will be made to the specified URL.
If no `url` is provided or it's empty, the widget will simply refresh.
After a successful URL call, the widget will automatically refresh.

**HTTP Headers**: The following headers are automatically included in button URL calls:
- `User-Agent: Appio-Android-Widget-{{APP_VERSION}}`
- `X-Service-Id: {{SERVICE_ID}}`
- `X-Device-Id: {{DEVICE_ID}}`
- `X-Customer-User-Id: {{CUSTOMER_USER_ID}}`

---
## lastUpdated

Displays the current time in HH:mm:ss format. This is a custom, non-native element.

| Property | Type | Description |
|----------|------|-------------|
| `fontSize` | `Int?` | Font size in sp |
| `fontWeight` | `String?` | Font weight: `normal`, `medium`, `semibold`, `bold` |
| `alignment` | `String?` | Text alignment: `start`, `center`, `end`, `left`, `right` |
| `color` | `String?` | Text [Color](#color) |
| `background` | `String?` | Background [Color](#color) |
| `padding` | `Padding?` | [Padding](#padding) |
| `width` | `Int\|String?` | Width in dp or `max` for fillMaxWidth |
| `height` | `Int\|String?` | Height in dp or `max` for fillMaxHeight |

---

## Padding

```json
"padding": {
  "top": 4, 
  "bottom": 4,
  "left": 8,
  "right": 8
}
```

| Field | Type | Description |
|-------|------|-------------|
| `top`, `bottom`, `left`, `right` | `Int?` | Edge-specific padding in dp |

---

## Color

Can be any of the following (defaults to `defaultTextColor` if invalid):

**Glance Theme Colors:**
- `primary`, `onprimary`, `primarycontainer`, `onprimarycontainer`
- `secondary`, `onsecondary`, `secondarycontainer`, `onsecondarycontainer`
- `tertiary`, `ontertiary`, `tertiarycontainer`, `ontertiarycontainer`
- `error`, `errorcontainer`, `onerror`, `onerrorcontainer`
- `background`, `onbackground`, `surface`, `onsurface`, `surfacevariant`, `onsurfacevariant`
- `outline`, `inverseonsurface`, `inversesurface`, `inverseprimary`, `widgetbackground`

**Standard Colors:**
- `clear`, `brown`, `indigo`, `mint`, `orange`, `pink`, `purple`

**Android Colors:**
- Any valid Android color name that can be parsed by `String.toColorInt()`
- `red`, `blue`, `green`, `black`, `white`, `gray`, `cyan`, `magenta`, `yellow`, `lightgray`, `darkgray`, `grey`, `lightgrey`, `darkgrey`, `aqua`, `fuchsia`, `lime`, `maroon`, `navy`, `olive`, `purple`, `silver`, `teal`

**Hex Colors:**
- Hex values (e.g. `"#00FF00"`, `"#aa00FF"`)

**Conditional Colors:**
- Light/dark mode colors as comma-separated string: `"{light},{dark}"` (e.g. `"#000000,#FFFFFF"`)

---

## Size Values

Properties like `width`, `height`, `size`, and `length` accept:
- **Integer**: Exact size in dp (e.g. `100`)
- **String**: `max` to fill available space

---

## Widget Variant Selection

Variants are automatically selected based on widget size:
1. Variants are sorted by total area (width × height) in descending order
2. The first variant that fits the available space is selected
3. If no variant fits, the first variant is used as fallback

## Example Configuration

```json
{
  "variants": [
    {
      "properties": {
        "width": 200,
        "height": 100,
        "background": "primary"
      },
      "elements": [
        {
          "type": "column",
          "properties": {
            "spacing": 8,
            "padding": {"top": 16, "bottom": 16, "left": 16, "right": 16}
          },
          "elements": [
            {
              "type": "text",
              "properties": {
                "text": "Service Status",
                "fontSize": 16,
                "fontWeight": "bold",
                "color": "onprimary"
              }
            },
            {
              "type": "refreshButton",
              "properties": {
                "style": "filled",
                "text": "Refresh"
              }
            }
          ]
        }
      ]
    }
  ]
}
```
