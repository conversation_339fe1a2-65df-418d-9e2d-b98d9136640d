# Appio Android Project Makefile
# 
# Usage:
#   make test          - Run all tests with detailed output
#   make test-unit     - Run only unit tests
#   make test-android  - Run only instrumented tests (requires device/emulator)
#   make test-clean    - Clean test artifacts and run fresh tests
#   make test-report   - Run tests and open HTML report
#   make help          - Show available commands

.PHONY: test test-unit test-android test-clean test-report help clean build

# Default target
.DEFAULT_GOAL := help

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Run all tests with detailed output
test:
	@echo "$(GREEN)Running all tests with detailed output...$(NC)"
	@echo "$(YELLOW)This includes both unit tests and will attempt instrumented tests if device is connected$(NC)"
	./gradlew test --info --continue --stacktrace
	@echo "$(GREEN)✓ Tests completed!$(NC)"
	@echo "$(YELLOW)View detailed HTML report at: app/build/reports/tests/testDebugUnitTest/index.html$(NC)"

# Run only unit tests
test-unit:
	@echo "$(GREEN)Running unit tests only...$(NC)"
	./gradlew testDebugUnitTest testReleaseUnitTest --info --continue --stacktrace
	@echo "$(GREEN)✓ Unit tests completed!$(NC)"
	@echo "$(YELLOW)View detailed HTML report at: app/build/reports/tests/testDebugUnitTest/index.html$(NC)"

# Run only instrumented tests (requires connected device/emulator)
test-android:
	@echo "$(GREEN)Running instrumented Android tests...$(NC)"
	@echo "$(YELLOW)Note: This requires a connected Android device or emulator$(NC)"
	./gradlew connectedDebugAndroidTest --info --continue --stacktrace
	@echo "$(GREEN)✓ Android tests completed!$(NC)"

# Clean test artifacts and run fresh tests
test-clean:
	@echo "$(GREEN)Cleaning test artifacts and running fresh tests...$(NC)"
	./gradlew clean
	./gradlew test --info --continue --stacktrace
	@echo "$(GREEN)✓ Clean tests completed!$(NC)"

# Run tests and open HTML report (macOS)
test-report:
	@echo "$(GREEN)Running tests and opening HTML report...$(NC)"
	./gradlew test --info --continue --stacktrace
	@if [ -f "app/build/reports/tests/testDebugUnitTest/index.html" ]; then \
		echo "$(GREEN)Opening test report in browser...$(NC)"; \
		open app/build/reports/tests/testDebugUnitTest/index.html; \
	else \
		echo "$(RED)Test report not found. Tests may have failed to run.$(NC)"; \
	fi

# Clean build artifacts
clean:
	@echo "$(GREEN)Cleaning build artifacts...$(NC)"
	./gradlew clean
	@echo "$(GREEN)✓ Clean completed!$(NC)"

# Build the project
build:
	@echo "$(GREEN)Building the project...$(NC)"
	./gradlew build --info
	@echo "$(GREEN)✓ Build completed!$(NC)"

# Show help
help:
	@echo "$(GREEN)Appio Android Project - Available Commands:$(NC)"
	@echo ""
	@echo "$(YELLOW)Testing:$(NC)"
	@echo "  make test          - Run all tests with detailed output"
	@echo "  make test-unit     - Run only unit tests (debug + release)"
	@echo "  make test-android  - Run only instrumented tests (requires device/emulator)"
	@echo "  make test-clean    - Clean test artifacts and run fresh tests"
	@echo "  make test-report   - Run tests and open HTML report (macOS)"
	@echo ""
	@echo "$(YELLOW)Building:$(NC)"
	@echo "  make build         - Build the project"
	@echo "  make clean         - Clean build artifacts"
	@echo ""
	@echo "$(YELLOW)Other:$(NC)"
	@echo "  make help          - Show this help message"
	@echo ""
	@echo "$(YELLOW)Examples:$(NC)"
	@echo "  make test                    # Run all tests"
	@echo "  make test-unit              # Run only unit tests"
	@echo "  make clean && make test     # Clean build and run tests"
	@echo ""
	@echo "$(YELLOW)Test Reports:$(NC)"
	@echo "  HTML Report: app/build/reports/tests/testDebugUnitTest/index.html"
	@echo "  XML Results: app/build/test-results/testDebugUnitTest/"
